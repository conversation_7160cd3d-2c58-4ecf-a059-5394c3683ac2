﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using NUnit.Framework;
using NFluent;
using System;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.Logging;
using FleetXQ.ServiceLayer;
using System.Collections.Generic;
using System.Linq.Expressions;
using FleetXQ.Tests.Common;
using QuickGraph;
using System.Linq;
using VDS.RDF;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Threading.Tasks;


namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VehicleAccessUtilitiesTests : TestBase
    {
        private IVehicleAccessUtilities _vehicleAccessUtilities;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"VehicleAccessUtilitiesTests-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _vehicleAccessUtilities = _serviceProvider.GetRequiredService<IVehicleAccessUtilities>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]

        public async Task UpdateVehicleDepartmentAccessesForPerson_GrantsAndRemovesAccessAsExpected()
        {
            // Arrange
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(persons, Is.Not.Null.And.Not.Empty, "Test data setup failed: No persons found.");
            var person = persons.FirstOrDefault();

            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found.");
            var departments = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true)).Take(2).ToList();
            Assert.That(departments.Count, Is.EqualTo(2), "Test data setup failed: Less than 2 departments found.");

            var permissionId = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault()?.Id ?? Guid.NewGuid();
            Assert.That(permissionId, Is.Not.EqualTo(Guid.Empty), "Test data setup failed: No permission found.");

            var updatedDepartmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            updatedDepartmentAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            updatedDepartmentAccesses.Add(new PersonToDepartmentVehicleNormalAccessViewDataObject(departments[0].Id, permissionId, person.Id) { HasAccess = true });
            updatedDepartmentAccesses.Add(new PersonToDepartmentVehicleNormalAccessViewDataObject(departments[1].Id, permissionId, person.Id) { HasAccess = false });

            Assert.That(updatedDepartmentAccesses, Is.Not.Null.And.Count.EqualTo(2), "updatedDepartmentAccesses not correctly initialized.");

            // Act

            var result = await _vehicleAccessUtilities.UpdateVehicleDepartmentAccessesForPersonAsync(person.Id, updatedDepartmentAccesses);

            // Assert
            Assert.That(result.Result, Is.True, "UpdateVehicleDepartmentAccessesForPerson should return true on success.");

            // Reload the person to verify changes
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> { "Driver.Card.DepartmentVehicleNormalCardAccessItems" }, skipSecurity: true);
            Assert.That(updatedPerson, Is.Not.Null, "Failed to reload the person for verification.");
            Assert.That(updatedPerson.Driver, Is.Not.Null, "Person's driver data is null.");
            Assert.That(updatedPerson.Driver.Card, Is.Not.Null, "Driver's card data is null.");
            Assert.That(updatedPerson.Driver.Card.DepartmentVehicleNormalCardAccessItems, Is.Not.Null, "Card's DepartmentVehicleNormalCardAccessItems is null.");

            // Verify that access was granted to the first department
            Assert.That(updatedPerson.Driver.Card.DepartmentVehicleNormalCardAccessItems.Any(a => a.DepartmentId == departments[0].Id), Is.True, "Access was not granted to the first department as expected.");

            // Verify that access was not granted or was removed for the second department
            Assert.That(updatedPerson.Driver.Card.DepartmentVehicleNormalCardAccessItems.Any(a => a.DepartmentId == departments[1].Id), Is.False, "Access was not removed from the second department as expected.");
        }


        [Test]
        public async Task UpdateVehicleModelAccessesForPerson_GrantsAndRemovesAccessAsExpected()
        {
            // Arrange
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(persons, Is.Not.Null.And.Not.Empty, "Test data setup failed: No persons found.");
            var person = persons.FirstOrDefault();
            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found.");

            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(department, Is.Not.Null, "Test data setup failed: No department found.");


            var models = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true)).Take(2).ToList();
            Assert.That(models.Count, Is.EqualTo(2), "Test data setup failed: Less than 2 models found.");

            var permissionId = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault()?.Id ?? Guid.NewGuid();

            var updatedModelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
            updatedModelAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            updatedModelAccesses.Add(new PersonToModelVehicleNormalAccessViewDataObject(department.Id, models[0].Id, permissionId) { HasAccess = true });
            updatedModelAccesses.Add(new PersonToModelVehicleNormalAccessViewDataObject(department.Id, models[1].Id, permissionId) { HasAccess = false });

            // Act
            var result = await _vehicleAccessUtilities.UpdateVehicleModelAccessesForPersonAsync(person.Id, updatedModelAccesses);

            // Assert
            Assert.That(result.Result, Is.True, "UpdateVehicleModelAccessesForPerson should return true on success.");

            // Reload the person to verify changes
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> { "Driver.Card.ModelVehicleNormalCardAccessItems" }, skipSecurity: true);

            // Verify that access was granted to the first model
            Assert.That(updatedPerson.Driver.Card.ModelVehicleNormalCardAccessItems.Any(a => a.ModelId == models[0].Id), Is.True, "Access was not granted to the first model as expected.");

            // Verify that access was not granted or was removed for the second model
            Assert.That(updatedPerson.Driver.Card.ModelVehicleNormalCardAccessItems.Any(a => a.ModelId == models[1].Id), Is.False, "Access was not removed from the second model as expected.");
        }


        [Test]
        public async Task UpdateVehiclePerVehicleAccessesForPerson_GrantsAndRemoveAccessAsExpected()
        {
            // Arrange
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(persons, Is.Not.Null.And.Not.Empty, "Test data setup failed: No persons found.");
            var person = persons.FirstOrDefault();
            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found.");
            var vehicles = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).Take(2).ToList();
            Assert.That(vehicles.Count, Is.EqualTo(2), "Test data setup failed: Less than 2 vehicles found.");

            var permissionId = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault()?.Id ?? Guid.NewGuid();

            // Ensure proper instantiation of DataObjectCollection
            var updatePerVehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
            updatePerVehicleAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            updatePerVehicleAccesses.Add(new PersonToPerVehicleNormalAccessViewDataObject(permissionId: permissionId, personId: person.Id, vehicleId: vehicles[0].Id) { HasAccess = true });
            updatePerVehicleAccesses.Add(new PersonToPerVehicleNormalAccessViewDataObject(permissionId: permissionId, personId: person.Id, vehicleId: vehicles[1].Id) { HasAccess = false });

            // Act
            var result = await _vehicleAccessUtilities.UpdateVehiclePerVehicleAccessesForPersonAsync(person.Id, updatePerVehicleAccesses);


            // Assert - Ensure the method returns true indicating success
            Assert.That(result.Result, Is.True, "UpdateVehiclePerVehicleAccessesForPerson should return true on success.");

            // Verify that access was granted to the first vehicle
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> { "Driver.Card.PerVehicleNormalCardAccessItems" }, skipSecurity: true);
            // Adjusted assertion to check for the presence of access in a possibly updated state
            Assert.That(updatedPerson.Driver.Card.PerVehicleNormalCardAccessItems.Any(a => a.VehicleId == vehicles[0].Id && !a.IsMarkedForDeletion), Is.True, "Access was not granted to the first vehicle as expected.");

            // Verify that access was not granted or was removed for the second vehicle
            Assert.That(updatedPerson.Driver.Card.PerVehicleNormalCardAccessItems.Any(a => a.VehicleId == vehicles[1].Id), Is.False, "Access was not removed from the second vehicle as expected.");
        }

        [Test]
        public async Task UpdateVehicleSiteAccessesForPerson_GrantsAndRemoveAccessAsExpected()
        {
            // Arrange
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(persons, Is.Not.Null.And.Not.Empty, "Test data setup failed: No persons found.");
            var person = persons.FirstOrDefault();

            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found.");
            var sites = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, skipSecurity: true)).ToList();
            Assert.That(sites.Count >= 2, Is.True, "Test data setup failed: Less than 2 sites found.");

            var permissionId = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault()?.Id ?? Guid.NewGuid();
            var updatedSiteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
            updatedSiteAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            updatedSiteAccesses.Add(new PersonToSiteVehicleNormalAccessViewDataObject(permissionId, person.Id, sites[0].Id) { HasAccess = true });
            updatedSiteAccesses.Add(new PersonToSiteVehicleNormalAccessViewDataObject(permissionId, person.Id, sites[1].Id) { HasAccess = false });

            // Act
            var result = await _vehicleAccessUtilities.UpdateVehicleSiteAccessesForPersonAsync(person.Id, updatedSiteAccesses);

            // Assert
            Assert.That(result.Result, Is.True, "UpdateVehicleSiteAccessesForPerson should return true on success.");

            // Reload the person to verify changes
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> { "Driver.Card.SiteVehicleNormalCardAccessItems" }, skipSecurity: true);

            // Verify that access was granted to the first site
            Assert.That(updatedPerson.Driver.Card.SiteVehicleNormalCardAccessItems.Any(a => a.SiteId == sites[0].Id), Is.True, "Access was not granted to the first site as expected.");

            // Verify that access was not granted or was removed for the second site
            Assert.That(updatedPerson.Driver.Card.SiteVehicleNormalCardAccessItems.Any(a => a.SiteId == sites[1].Id), Is.False, "Access was not removed from the second site as expected.");
        }

        [Test]
        public async Task RemoveAllVehicleAccessesForPerson_RemovesAllAccessesAsExpected()
        {
            // Arrange
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(persons, Is.Not.Null.And.Not.Empty, "Test data setup failed: No persons found.");
            var person = persons.FirstOrDefault();

            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found.");
            var allVehicles = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).ToList();
            var updatePerVehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
            updatePerVehicleAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            foreach (var vehicle in allVehicles)
            {
                updatePerVehicleAccesses.Add(new PersonToPerVehicleNormalAccessViewDataObject(permissionId: Guid.NewGuid(), personId: person.Id, vehicleId: vehicle.Id) { HasAccess = false });
            }

            // Act
            var result = await _vehicleAccessUtilities.UpdateVehiclePerVehicleAccessesForPersonAsync(person.Id, updatePerVehicleAccesses);

            // Assert
            Assert.That(result.Result, Is.True, "Removing all vehicle accesses should return true on success.");
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> { "Driver.Card.PerVehicleNormalCardAccessItems" }, skipSecurity: true);
            Assert.That(updatedPerson.Driver.Card.PerVehicleNormalCardAccessItems.Count, Is.EqualTo(0), "All vehicle accesses were not removed as expected.");
        }

        [Test]
        public async Task UpdateAccessForNonExistentPerson_ThrowsExpectedException()
        {
            // Arrange
            var nonExistentPersonId = Guid.NewGuid();
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");
            var updatePerVehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
            updatePerVehicleAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            updatePerVehicleAccesses.Add(new PersonToPerVehicleNormalAccessViewDataObject(permissionId: Guid.NewGuid(), personId: nonExistentPersonId, vehicleId: vehicle.Id) { HasAccess = true });

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () =>
            {
                await _vehicleAccessUtilities.UpdateVehiclePerVehicleAccessesForPersonAsync(nonExistentPersonId, updatePerVehicleAccesses);
            }, "Expected GOServerException was not thrown.");

            Assert.That(ex.Message, Does.Contain("person who is a driver and has an access card"), "Exception message does not contain expected text.");
        }

        [Test]
        public async Task GetAccessesForDepartmentsAsync_ReturnsExpectedDepartmentAccesses()
        {
            // Arrange
            var person = await CreateTestPersonAsync();
            Assert.That(person, Is.Not.Null, "Test data setup failed: Failed to create test person.");

            var sites = await _dataFacade.SiteDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(sites, Is.Not.Null.And.Not.Empty, "Test data setup failed: No sites found.");

            // Get the permission for NormalDriver level
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            Assert.That(permission, Is.Not.Null, "Test data setup failed: No permission found for NormalDriver level.");

            var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
            siteAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            siteAccesses.Add(new PersonToSiteVehicleNormalAccessViewDataObject(permission.Id, person.Id, sites.First().Id) { HasAccess = true });

            // Act
            var result = await _vehicleAccessUtilities.GetAccessesForDepartmentsAsync(siteAccesses, person.Id, (int)PermissionLevelEnum.NormalDriver);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Result.Count, Is.GreaterThan(0), "Should return at least one department access");
            Assert.That(result.Result.All(a => a.PersonId == person.Id), Is.True, "All accesses should be for the specified person");
            Assert.That(result.Result.All(a => a.PermissionId == permission.Id), Is.True, "All accesses should have the correct permission");
        }

        [Test]
        public async Task GetAccessesForModelsAsync_ReturnsExpectedModelAccesses()
        {
            // Arrange
            var person = await CreateTestPersonAsync();
            Assert.That(person, Is.Not.Null, "Test data setup failed: Failed to create test person.");

            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(departments, Is.Not.Null.And.Not.Empty, "Test data setup failed: No departments found.");

            // Get the permission for NormalDriver level
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            Assert.That(permission, Is.Not.Null, "Test data setup failed: No permission found for NormalDriver level.");

            // Create department accesses for all departments
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            departmentAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            foreach (var department in departments)
            {
                departmentAccesses.Add(new PersonToDepartmentVehicleNormalAccessViewDataObject(department.Id, permission.Id, person.Id) { HasAccess = true });
            }

            // Act
            var result = await _vehicleAccessUtilities.GetAccessesForModelsAsync(departmentAccesses, person.Id, (int)PermissionLevelEnum.NormalDriver);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Result.Count, Is.GreaterThan(0), "Should return at least one model access");
            Assert.That(result.Result.All(a => a.PersonId == person.Id), Is.True, "All accesses should be for the specified person");
            Assert.That(result.Result.All(a => a.PermissionId == permission.Id), Is.True, "All accesses should have the correct permission");
        }

        [Test]
        public async Task GetAccessesForVehiclesAsync_ReturnsExpectedVehicleAccesses()
        {
            // Arrange
            var person = await CreateTestPersonAsync();
            Assert.That(person, Is.Not.Null, "Test data setup failed: Failed to create test person.");

            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(departments, Is.Not.Null.And.Not.Empty, "Test data setup failed: No departments found.");

            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(models, Is.Not.Null.And.Not.Empty, "Test data setup failed: No models found.");

            // Get the permission for NormalDriver level
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            Assert.That(permission, Is.Not.Null, "Test data setup failed: No permission found for NormalDriver level.");

            // Create department accesses for all departments
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            departmentAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            foreach (var department in departments)
            {
                departmentAccesses.Add(new PersonToDepartmentVehicleNormalAccessViewDataObject(department.Id, permission.Id, person.Id) { HasAccess = true });
            }

            // Create model accesses for all models in the first department
            var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
            modelAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            foreach (var model in models)
            {
                modelAccesses.Add(new PersonToModelVehicleNormalAccessViewDataObject(departments.First().Id, model.Id, permission.Id) { HasAccess = true });
            }

            // Act
            var result = await _vehicleAccessUtilities.GetAccessesForVehiclesAsync(departmentAccesses, modelAccesses, person.Id, (int)PermissionLevelEnum.NormalDriver);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "Result should not be null");
            Assert.That(result.Result.Count, Is.GreaterThan(0), "Should return at least one vehicle access");
            Assert.That(result.Result.All(a => a.PersonId == person.Id), Is.True, "All accesses should be for the specified person");
            Assert.That(result.Result.All(a => a.PermissionId == permission.Id), Is.True, "All accesses should have the correct permission");
        }

        [Test]
        public async Task UpdateAccessesForPersonAsync_UpdatesAllAccessTypesCorrectly()
        {
            // Arrange
            var person = await CreateTestPersonAsync();
            Assert.That(person, Is.Not.Null, "Test data setup failed: Failed to create test person.");

            var sites = await _dataFacade.SiteDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(sites, Is.Not.Null.And.Not.Empty, "Test data setup failed: No sites found.");

            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(departments, Is.Not.Null.And.Not.Empty, "Test data setup failed: No departments found.");

            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(models, Is.Not.Null.And.Not.Empty, "Test data setup failed: No models found.");

            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(vehicles, Is.Not.Null.And.Not.Empty, "Test data setup failed: No vehicles found.");

            // Get the permission for NormalDriver level
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            Assert.That(permission, Is.Not.Null, "Test data setup failed: No permission found for NormalDriver level.");

            // Create site accesses
            var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>();
            siteAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            siteAccesses.Add(new PersonToSiteVehicleNormalAccessViewDataObject(permission.Id, person.Id, sites.First().Id) { HasAccess = true });

            // Create department accesses
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            departmentAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            departmentAccesses.Add(new PersonToDepartmentVehicleNormalAccessViewDataObject(departments.First().Id, permission.Id, person.Id) { HasAccess = true });

            // Create model accesses
            var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>();
            modelAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            modelAccesses.Add(new PersonToModelVehicleNormalAccessViewDataObject(departments.First().Id, models.First().Id, permission.Id) { HasAccess = true });

            // Create vehicle accesses
            var vehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>();
            vehicleAccesses.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            vehicleAccesses.Add(new PersonToPerVehicleNormalAccessViewDataObject(permission.Id, person.Id, vehicles.First().Id) { HasAccess = true });

            // Act
            var result = await _vehicleAccessUtilities.UpdateAccessesForPersonAsync(
                siteAccesses,
                departmentAccesses,
                modelAccesses,
                vehicleAccesses,
                person.Id);

            // Assert
            Assert.That(result.Result, Is.True, "Update should return true on success");

            // Verify the updates by reloading the person
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> {
                "Driver.Card.SiteVehicleNormalCardAccessItems",
                "Driver.Card.DepartmentVehicleNormalCardAccessItems",
                "Driver.Card.ModelVehicleNormalCardAccessItems",
                "Driver.Card.PerVehicleNormalCardAccessItems"
            }, skipSecurity: true);

            // Verify site access
            Assert.That(updatedPerson.Driver.Card.SiteVehicleNormalCardAccessItems.Any(a => a.SiteId == sites.First().Id), Is.True, "Site access was not granted");

            // Verify department access
            Assert.That(updatedPerson.Driver.Card.DepartmentVehicleNormalCardAccessItems.Any(a => a.DepartmentId == departments.First().Id), Is.True, "Department access was not granted");

            // Verify model access
            Assert.That(updatedPerson.Driver.Card.ModelVehicleNormalCardAccessItems.Any(a => a.ModelId == models.First().Id), Is.True, "Model access was not granted");

            // Verify vehicle access
            Assert.That(updatedPerson.Driver.Card.PerVehicleNormalCardAccessItems.Any(a => a.VehicleId == vehicles.First().Id), Is.True, "Vehicle access was not granted");
        }

        private async Task CreateTestDataAsync()

        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();


            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            var sites = new List<string> { "Site 1", "Site 2", "Site 3" };
            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };
            var PersonFirstName1 = new string[] { "John", "Peter", "Paul", "Mark", "Luke", "Matthew", "James", "Jude", "Simon", "Andrew" };
            var PersonFirstName2 = new string[] { "Mary", "Elizabeth", "Anna", "Ruth", "Esther", "Sarah", "Rebecca", "Leah", "Rachel", "Deborah" };
            var PersonFirstName3 = new string[] { "David", "Solomon", "Elijah", "Elisha", "Isaiah", "Jeremiah", "Ezekiel", "Daniel", "Hosea", "Joel" };
            var PersonLastName1 = new string[] { "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor" };
            var PersonLastName2 = new string[] { "Anderson", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", "Garcia", "Martinez", "Robinson" };
            var PersonLastName3 = new string[] { "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez", "King" };

            foreach (var siteName in sites)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.CustomerId = customer.Id;
                site.Name = siteName;
                site.TimezoneId = timeZone.Id;
                site.Id = Guid.NewGuid();

                await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

                var departmentNames = new List<string> { "Warehouse", "Logistics", "Production" };

                // create 3 departments for each site
                for (int j = 0; j < 3; j++)
                {
                    var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                    department.Id = Guid.NewGuid();
                    department.Name = siteName + " " + departmentNames[j];
                    department.SiteId = site.Id;
                    await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

                    // get only 3 models
                    var Models = new List<ModelDataObject>();

                    for (int i = 0; i < 3; i++)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = "Model " + (i + 1).ToString();
                        model.Description = "Description for Model " + (i + 1).ToString();
                        model.DealerId = dealer.Id;
                        // Assigning Model.Type based on the ModelTypesEnum
                        switch (i)
                        {
                            case 0:
                                model.Type = ModelTypesEnum.Electric;
                                break;
                            case 1:
                                model.Type = ModelTypesEnum.ICForklifts;
                                break;
                            case 2:
                                model.Type = ModelTypesEnum.OrderPickers;
                                break;
                            // Additional cases can be added here for other types if necessary
                            default:
                                model.Type = ModelTypesEnum.PalletJack; // Default case if more than 3 models are created
                                break;
                        }
                        // Removed setting the non-existent Active property
                        await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
                        Models.Add(model);
                    }

                    // create 10 vehicles for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = Guid.NewGuid();
                        vehicle.CustomerId = customer.Id;
                        vehicle.SiteId = site.Id;
                        vehicle.DepartmentId = department.Id;
                        vehicle.IDLETimer = 300;
                        vehicle.OnHire = true;
                        vehicle.ImpactLockout = true;
                        // set random modelId with index rand 0 to 2
                        if (Models.Any())
                        {
                            vehicle.ModelId = Models[k % 3].Id;
                        }
                        else
                        {
                            throw new InvalidOperationException("No models found to assign to vehicle.");
                        }
                        if (j == 0)
                        {
                            vehicle.HireNo = VehicleHireNos1[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos1[k] + department.Id;
                        }
                        else if (j == 1)
                        {
                            vehicle.HireNo = VehicleHireNos2[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos2[k] + department.Id;
                        }
                        else
                        {
                            vehicle.HireNo = VehicleHireNos3[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos3[k] + department.Id;
                        }

                        // create a module for the vehicle
                        var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                        module.Id = Guid.NewGuid();
                        module.Calibration = 100;
                        module.CCID = "CCID" + j + k;
                        // set FSSSBASE random from 100000 to 200000 in increment of 10000
                        Random random = new Random();
                        int randomNumber = random.Next(10, 21);
                        module.FSSSBase = randomNumber * 10000;
                        module.FSSXMulti = 1;
                        if (j == 0)
                            module.IoTDevice = IoTHubIds1[k] + department.Id;
                        else if (j == 1)
                            module.IoTDevice = IoTHubIds2[k] + department.Id;
                        else
                            module.IoTDevice = IoTHubIds3[k] + department.Id;
                        module.IsAllocatedToVehicle = true;
                        await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

                        vehicle.ModuleId1 = module.Id;
                        await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    }
                    // create 10 persons for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                        person.Id = Guid.NewGuid();
                        person.CustomerId = customer.Id;
                        person.SiteId = site.Id;
                        person.DepartmentId = department.Id;
                        if (j == 0)
                        {
                            person.FirstName = PersonFirstName1[k];
                            person.LastName = PersonLastName1[k];
                        }
                        else if (j == 1)
                        {
                            person.FirstName = PersonFirstName2[k];
                            person.LastName = PersonLastName2[k];
                        }
                        else
                        {
                            person.FirstName = PersonFirstName3[k];
                            person.LastName = PersonLastName3[k];
                        }
                        person.IsDriver = true;
                        person.IsActiveDriver = true;

                        person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true); //Crear person and driver

                        var card = _serviceProvider.GetRequiredService<CardDataObject>();
                        card.Id = Guid.NewGuid();
                        // Facility Code is random between 1 to 254 in string
                        Random random = new Random();
                        card.FacilityCode = random.Next(1, 255).ToString();
                        // Card Number is random between 100001 to 675899 in string
                        card.CardNumber = random.Next(100001, 675900).ToString();
                        card.Active = true;
                        card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                        card.Type = CardTypeEnum.CardID;

                        card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

                        // get the driver object from person and assign the card ID to the driver
                        var driver = person.Driver;
                        driver.CardDetailsId = card.Id;
                        driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
                    }
                }
            }
        }

        private async Task<PersonDataObject> CreateTestPersonAsync()
        {
            // Create country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Create timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            // Create site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Create department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

            // Create person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "Test";
            person.LastName = "Person";
            person.IsDriver = true;
            person.IsActiveDriver = true;
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;

            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Create card and associate it with the driver
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.FacilityCode = "1";
            card.CardNumber = "123456";
            card.Active = true;
            card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
            card.Type = CardTypeEnum.CardID;
            card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            // Get the driver that was automatically created and associate the card
            var driver = person.Driver;
            driver.CardDetailsId = card.Id;
            await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);

            return person;
        }
    }

}