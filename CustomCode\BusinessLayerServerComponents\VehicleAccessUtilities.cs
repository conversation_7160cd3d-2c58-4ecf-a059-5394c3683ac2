﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NHibernate.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Newtonsoft.Json;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;

namespace FleetXQ.BusinessLayer.Components.Server
{

    /// <summary>
	/// VehicleAccessUtilities Component
	///  
	/// </summary>
    public partial class VehicleAccessUtilities : BaseServerComponent, IVehicleAccessUtilities
    {
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly ILoggingService _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        private readonly IAuthentication _authentication;

        public VehicleAccessUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger, IServiceScopeFactory serviceScopeFactory, IAuthentication authentication) : base(serviceProvider, configuration, dataFacade)
        {
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _authentication = authentication;
        }

        /////////////////////////////////////////////////////////////////////////////////////
        // New methods for updating site, department, model and vehicle accesses in batch
        /////////////////////////////////////////////////////////////////////////////////////

        public async Task<ComponentResponse<bool>> CopyUserVehicleAccessAsync(Guid personId, Guid[] driverIds, Dictionary<string, object> parameters = null)
        {
            var person = await GetPersonAsync(personId);

            foreach (var driverId in driverIds)
            {
                var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
                driver.Id = driverId;
                driver = await _dataFacade.DriverDataProvider.GetAsync(driver, includes: new List<string> {
                    "Card",
                    "Card.SiteVehicleNormalCardAccessItems",
                    "Card.DepartmentVehicleNormalCardAccessItems",
                    "Card.ModelVehicleNormalCardAccessItems",
                    "Card.PerVehicleNormalCardAccessItems" });

                await driver.LoadCardAsync(skipSecurity: true);

                if (driver.Card == null)
                {
                    continue;
                }

                // Delete current accesses
                foreach (var item in driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.PerVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                //Copy accesses
                foreach (var item in person.Driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                    siteAccess.SiteId = item.SiteId;
                    siteAccess.PermissionId = item.PermissionId;

                    driver.Card.SiteVehicleNormalCardAccessItems.Add(siteAccess);
                }

                foreach (var item in person.Driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                    deptAccess.DepartmentId = item.DepartmentId;
                    deptAccess.PermissionId = item.PermissionId;
                    driver.Card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);
                }

                foreach (var item in person.Driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    modelAccess.ModelId = item.ModelId;
                    modelAccess.DepartmentId = item.DepartmentId;
                    modelAccess.PermissionId = item.PermissionId;
                    driver.Card.ModelVehicleNormalCardAccessItems.Add(modelAccess);
                }

                foreach (var item in person.Driver.Card.PerVehicleNormalCardAccessItems)
                {
                    var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    vehicleAccess.VehicleId = item.VehicleId;
                    vehicleAccess.PermissionId = item.PermissionId;

                    driver.Card.PerVehicleNormalCardAccessItems.Add(vehicleAccess);
                }

                await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
            }

            return new ComponentResponse<System.Boolean>(true);
        }

        public async Task<ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>> CreateOnDemandAccessesAsync(Guid[] cardIds, Guid vehicleId, Dictionary<string, object> parameters)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            if (vehicle == null)
            {
                return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>());
            }

            var permissionOnDemandUser = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();

            var cardIdsToAdd = new List<Guid>();

            var accessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var cardId in cardIds)
            {
                var card = _serviceProvider.GetRequiredService<CardDataObject>();
                card.Id = cardId;
                card = await _dataFacade.CardDataProvider.GetAsync(card);

                if (card == null)
                {
                    continue;
                }

                await card.LoadPerVehicleNormalCardAccessItemsAsync();

                var existingPerVehicleAccess = card.PerVehicleNormalCardAccessItems.FirstOrDefault(a => a.VehicleId == vehicleId && a.PermissionId == permissionOnDemandUser.Id);
                if (existingPerVehicleAccess == null)
                {
                    existingPerVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    existingPerVehicleAccess.VehicleId = vehicleId;
                    existingPerVehicleAccess.PermissionId = permissionOnDemandUser.Id;
                    card.PerVehicleNormalCardAccessItems.Add(existingPerVehicleAccess);

                    await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
                }

                accessList.Add(existingPerVehicleAccess);
            }

            // Start sync in background
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            _ = Task.Run(async () =>
            {
                var syncStart = DateTime.UtcNow;
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                    var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                    await SyncDriversInScopeAsync(new List<Guid> { vehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand create) completed in {syncDuration}ms for vehicle {vehicleId}");
                }
                catch (Exception ex)
                {
                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand create) failed after {syncDuration}ms: {ex.Message}");
                }
            });

            return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>(accessList));
        }

        public async Task<ComponentResponse<bool>> DeleteOnDemandAccessAsync(Guid accessId, Dictionary<string, object> parameters = null)
        {
            var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            access.Id = accessId;
            access = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetAsync(access);

            if (access != null)
            {
                access.IsMarkedForDeletion = true;

                await _dataFacade.PerVehicleNormalCardAccessDataProvider.DeleteAsync(access);

                // Start sync in background
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                _ = Task.Run(async () =>
                {
                    var syncStart = DateTime.UtcNow;
                    try
                    {
                        using var scope = _serviceScopeFactory.CreateScope();
                        var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                        var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                        await SyncDriversInScopeAsync(new List<Guid> { access.VehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand delete) completed in {syncDuration}ms for vehicle {access.VehicleId}");
                    }
                    catch (Exception ex)
                    {
                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand delete) failed after {syncDuration}ms: {ex.Message}");
                    }
                });

                return new ComponentResponse<bool>(true);
            }

            return new ComponentResponse<bool>(false);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>> GetAccessesForDepartmentsAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                return default;
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var departmentAccessCollection = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            foreach (var siteAccess in personToSiteAccesses)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = siteAccess.SiteId;
                site = await _dataFacade.SiteDataProvider.GetAsync(site, includes: new List<string> { "DepartmentItems" }, skipSecurity: true);
                if (site == null)
                {
                    continue;
                }

                var departments = site.DepartmentItems;

                var departmentVehicleAccesses = departments.Select(department =>
                {
                    var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();

                    deptAccess.DepartmentId = department.Id;
                    deptAccess.DepartmentName = department.Name;
                    deptAccess.PermissionId = permission.Id;
                    deptAccess.PersonId = personId;
                    deptAccess.HasAccess = true;
                    deptAccess.IsNew = false; // Set to false to prevent subscription handlers from running

                    // Properly add the object to the dataset's internal structure
                    dataset.AddObject(deptAccess);

                    return deptAccess;
                }).ToList();

                departmentAccessCollection.AddRange(departmentVehicleAccesses);
            }

            return new ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>(departmentAccessCollection);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>> GetAccessesForModelsAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                return default;
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var modelAccessCollection = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var models = new List<ModelDataObject>();
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var vehicles = department.Vehicles;

                foreach (var vehicle in vehicles)
                {
                    var model = await vehicle.LoadModelAsync();
                    if (model != null && !models.Exists(x => x.Id == model.Id))
                    {
                        models.Add(model);
                    }
                }

                var accessesToAdd = models
                    .Select(model =>
                    {
                        var access = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        access.ModelId = model.Id;
                        access.ModelName = $"{department.Name} : {model.Name}";
                        access.PermissionId = permission.Id;
                        access.DepartmentId = department.Id;
                        access.PersonId = personId;
                        access.HasAccess = true;
                        access.IsNew = false; // Set to false to prevent subscription handlers from running

                        // Properly add the object to the dataset's internal structure
                        dataset.AddObject(access);

                        return access;
                    })
                    .ToList();

                modelAccessCollection.AddRange(accessesToAdd);
            }

            return new ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>(modelAccessCollection);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>> GetAccessesForVehiclesAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                return default;
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var vehicleAccessCollection = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            var vehicles = new List<VehicleDataObject>();

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var modelIds = personToModelAccesses.Select(a => a.ModelId).ToList();

                foreach (var vehicle in department.Vehicles)
                {
                    if (!vehicles.Exists(x => x.Id == vehicle.Id) && modelIds.Contains(vehicle.ModelId))
                    {
                        vehicles.Add(vehicle);
                    }
                }
            }

            var perVehicleAccessList = vehicles
                .Select(vehicle =>
                {
                    var access = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                    access.VehicleId = vehicle.Id;
                    access.HireNo = vehicle.HireNo;
                    access.PermissionId = permission.Id;
                    access.PersonId = personId;
                    access.HasAccess = true;
                    access.IsNew = false; // Set to false to prevent subscription handlers from running

                    // Properly add the object to the dataset's internal structure
                    dataset.AddObject(access);

                    return access;
                })
                .ToList();

            vehicleAccessCollection.AddRange(perVehicleAccessList);

            return new ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>(vehicleAccessCollection);
        }

        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses,
            Guid personId,
            Dictionary<string, object> parameters = null)
        {
            try
            {
                _logger?.LogInformation($"[PERF] Starting user access update queue operation for person {personId}");

                // Get the current user for tracking
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                // Get the person to extract customer ID
                var person = await GetPersonAsync(personId);

                // Create the user access update message
                var correlationId = Guid.NewGuid().ToString();
                var userAccessMessage = new UserAccessUpdateMessage
                {
                    PersonId = personId,
                    CustomerId = person.CustomerId,
                    PersonToSiteAccessesJson = SerializeAccessCollection(personToSiteAccesses),
                    PersonToDepartmentAccessesJson = SerializeAccessCollection(personToDepartmentAccesses),
                    PersonToModelAccessesJson = SerializeAccessCollection(personToModelAccesses),
                    PersonToVehicleAccessesJson = CompressAccessCollection(personToVehicleAccesses),
                    CreatedAt = DateTime.UtcNow,
                    InitiatedByUserId = currentUserId,
                    CorrelationId = correlationId,
                    Priority = "Normal"
                };

                // Send message to queue
                var queueService = _serviceProvider.GetRequiredService<IUserAccessQueueService>();
                await queueService.SendUserAccessUpdateMessageAsync(userAccessMessage);

                _logger?.LogInformation($"[PERF] User access update message queued for person {personId} with correlation {correlationId}");

                return new ComponentResponse<bool>(true);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to queue user access update for person {personId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Internal method that performs the actual user access update processing
        /// This method is called by the Azure Function to process queued messages
        /// </summary>
        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonInternalAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses,
            Guid personId,
            Dictionary<string, object> parameters = null)
        {
            _logger?.LogInformation($"[PERF] Starting internal user access update processing for person {personId}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Sites
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingSiteAccesses;
            try
            {
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.SiteId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate site access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .GroupBy(access => (access.SiteId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate site access records. " +
                    $"Created dictionary with {existingSiteAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (sitesToAdd, sitesToRemove) = await CategorizeAccessUpdates(
                personToSiteAccesses,
                existingSiteAccesses,
                person);

            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var siteAccess in sitesToRemove)
            {
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;
            }

            // Departments
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingDepartmentAccesses;
            try
            {
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate department access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .GroupBy(access => (access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate department access records. " +
                    $"Created dictionary with {existingDepartmentAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (departmentsToAdd, departmentsToRemove) = await CategorizeAccessUpdates(
                personToDepartmentAccesses,
                existingDepartmentAccesses,
                person);

            foreach (var deptAccess in departmentsToAdd)
            {
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var deptAccess in departmentsToRemove)
            {
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;
            }

            // Models
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingModelAccesses;
            try
            {
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.ModelId, access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate model access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .GroupBy(access => (access.ModelId, access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate model access records. " +
                    $"Created dictionary with {existingModelAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (modelsToAdd, modelsToRemove) = await CategorizeAccessUpdates(
                personToModelAccesses,
                existingModelAccesses,
                person);

            foreach (var modelAccess in modelsToAdd)
            {
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var modelAccess in modelsToRemove)
            {
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;
            }

            // Vehicles
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingVehicleAccesses;
            try
            {
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.VehicleId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate vehicle access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .GroupBy(access => (access.VehicleId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate vehicle access records. " +
                    $"Created dictionary with {existingVehicleAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (vehiclesToAdd, vehiclesToRemove) = await CategorizeAccessUpdates(
                personToVehicleAccesses,
                existingVehicleAccesses,
                person);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            try
            {
                await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
                _logger?.LogInformation($"[PERF] Successfully saved person access changes for PersonId: {personId}");
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate key detected during person access save for PersonId: {personId}. " +
                    $"This indicates data integrity issues with duplicate access records. Skipping access save but continuing with sync operations. " +
                    $"Key details: {argEx.Message}");

                // Continue with sync operations even if save failed due to duplicate keys
            }
            catch (Exception saveEx)
            {
                _logger?.LogError(saveEx, $"[PERF] Unexpected error during person access save for PersonId: {personId}: {saveEx.Message}");
                throw; // Re-throw other exceptions as they indicate more serious issues
            }

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = "UserAccessUpdate",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count
                    };
                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {personId} with correlation {correlationId}");
            }

            _logger?.LogInformation($"[PERF] Internal user access update processing completed for person {personId}");
            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// Serializes access collection to JSON string
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="collection">The collection to serialize</param>
        /// <returns>JSON string representation of the collection</returns>
        private string SerializeAccessCollection<T>(DataObjectCollection<T> collection) where T : class, IDataObject
        {
            if (collection == null || collection.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var items = collection.Cast<T>().ToList();
                // Use Newtonsoft.Json which handles complex object graphs better
                return JsonConvert.SerializeObject(items, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Ignore
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to serialize access collection of type {typeof(T).Name}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Compresses access collection to Base64-encoded GZip string to reduce message size
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="collection">The collection to compress</param>
        /// <returns>Base64-encoded compressed JSON string</returns>
        private string CompressAccessCollection<T>(DataObjectCollection<T> collection) where T : class, IDataObject
        {
            if (collection == null || collection.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var items = collection.Cast<T>().ToList();

                // Serialize to JSON first
                var json = JsonConvert.SerializeObject(items, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Ignore
                });

                var originalSize = Encoding.UTF8.GetByteCount(json);

                // Compress using GZip
                using (var output = new MemoryStream())
                {
                    using (var gzip = new GZipStream(output, CompressionLevel.Optimal))
                    using (var writer = new StreamWriter(gzip, Encoding.UTF8))
                    {
                        writer.Write(json);
                    }

                    var compressedBytes = output.ToArray();
                    var compressedBase64 = Convert.ToBase64String(compressedBytes);
                    var compressedSize = compressedBytes.Length;

                    stopwatch.Stop();
                    var compressionRatio = (double)originalSize / compressedSize;
                    _logger?.LogInformation($"[PERF] Compressed {typeof(T).Name} collection: {originalSize} bytes -> {compressedSize} bytes (ratio: {compressionRatio:F2}x) in {stopwatch.ElapsedMilliseconds}ms");

                    return compressedBase64;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to compress access collection of type {typeof(T).Name}, falling back to regular serialization");
                // Fallback to regular serialization if compression fails
                return SerializeAccessCollection(collection);
            }
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleDepartmentAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing department accesses
            var existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                .ToDictionary(
                    access => (access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize department access updates
            var (departmentsToAdd, departmentsToRemove) = await CategorizeAccessUpdates(
                updatedPersonToDepartmentAccesses,
                existingDepartmentAccesses,
                person);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var deptAccess in departmentsToAdd)
            {
                // Create and add department access
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Process model and vehicle access
                await AddAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var deptAccess in departmentsToRemove)
            {
                // Mark existing access for deletion
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Remove model and vehicle access
                await RemoveAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                    card,
                    deptAccess.DepartmentId,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = "DepartmentAccessUpdate",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {personId} (department access update) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleModelAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing model accesses using composite key
            var existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                .ToDictionary(
                    access => (access.ModelId, access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize model access updates
            var (modelsToAdd, modelsToRemove) = await CategorizeAccessUpdates(
                updateModelAccesses,
                existingModelAccesses,
                person);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var modelAccess in modelsToAdd)
            {
                // Create and add model access
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);

                // Add vehicle access
                var vehicleAccesses = await AddAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var modelAccess in modelsToRemove)
            {
                // Mark existing access for deletion
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;

                // Remove vehicle access
                var vehicleAccesses = await RemoveAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = "ModelAccessUpdate",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {personId} (model access update) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehiclePerVehicleAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing vehicle accesses
            var existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                .ToDictionary(
                    access => (access.VehicleId, access.PermissionId),
                    access => access
                );

            // Categorize vehicle access updates
            var (vehiclesToAdd, vehiclesToRemove) = await CategorizeAccessUpdates(
                updatePerVehicleAccesses,
                existingVehicleAccesses,
                person);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            // Process removals
            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = "VehicleAccessUpdate",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {personId} (vehicle access update) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleSiteAccessesForPersonAsync(
                                                            Guid personId,
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses,
            Dictionary<string, object> parameters = null)
        {
            var methodStartTime = DateTime.UtcNow;
            _logger?.LogInformation($"[PERF] Starting UpdateVehicleSiteAccessesForPersonAsync for person {personId} with {updatedPersonToSiteAccesses.Count} access changes");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing site accesses
            var existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                .ToDictionary(
                    access => (access.SiteId, access.PermissionId),
                    access => access
                );

            // Categorize site access updates
            var (sitesToAdd, sitesToRemove) = await CategorizeAccessUpdates(
                updatedPersonToSiteAccesses,
                existingSiteAccesses,
                person);

            // Process all access changes and collect vehicle updates
            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            var additionsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);

                var vehicleAccesses = await AddAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var additionsDuration = (DateTime.UtcNow - additionsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site additions took {additionsDuration}ms for {sitesToAdd.Count} sites");

            // Process removals
            var removalsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToRemove)
            {
                // Mark existing access for deletion
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;

                var vehicleAccesses = await RemoveAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var removalsDuration = (DateTime.UtcNow - removalsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site removals took {removalsDuration}ms for {sitesToRemove.Count} sites");

            // Save changes and sync drivers
            var saveStart = DateTime.UtcNow;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
            var saveDuration = (DateTime.UtcNow - saveStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] PersonDataProvider.SaveAsync took {saveDuration}ms");

            var totalDuration = (DateTime.UtcNow - methodStartTime).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] UpdateVehicleSiteAccessesForPersonAsync completed in {totalDuration}ms for person {personId}");

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = "SiteAccessUpdate",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[PERF] {vehicleIds.Count} vehicle sync messages queued for person {personId} (site access update) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Get site with departments in a single query
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = siteId;
            site = await _dataFacade.SiteDataProvider.GetAsync(
                site,
                includes: new List<string> { "DepartmentItems" },
                skipSecurity: true
            ) ?? throw new GOServerException($"Site {siteId} not found");

            var allPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process departments sequentially to avoid MARS issues
            foreach (var department in site.DepartmentItems)
            {
                // Create and add department access
                var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                deptAccess.DepartmentId = department.Id;
                deptAccess.PermissionId = permissionId;
                card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);

                // Execute operations sequentially
                await AddAccessForModelsOfDepartmentAsync(card, department, permissionId);
                var perVehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(card, department, permissionId);

                if (perVehicleAccesses.Any())
                {
                    allPerVehicleAccesses.AddRange(perVehicleAccesses);
                }
            }

            return allPerVehicleAccesses;
        }

        private async System.Threading.Tasks.Task AddAccessForModelsOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var modelIds = (await department.LoadVehiclesAsync(skipSecurity: true))
                .Select(v => v.ModelId)
                .ToHashSet();

            var existingAccess = card.ModelVehicleNormalCardAccessItems
               .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
               .Select(a => a.ModelId)
               .ToHashSet();

            var modelsNeedingAccess = modelIds.Except(existingAccess);

            var accessesToAdd = modelsNeedingAccess
                .Select(modelId =>
                {
                    var access = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    access.ModelId = modelId;
                    access.PermissionId = permissionId;
                    access.DepartmentId = department.Id;
                    return access;
                })
                .ToList();

            if (accessesToAdd.Any())
            {
                card.ModelVehicleNormalCardAccessItems.AddRange(accessesToAdd);
            }
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var vehicles = await department.LoadVehiclesAsync(skipSecurity: true);

            // Create all access items at once using LINQ
            var perVehicleAccessList = vehicles
                .Select(vehicle =>
                {
                    var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    access.VehicleId = vehicle.Id;
                    access.PermissionId = permissionId;
                    return access;
                })
                .ToList();

            // Add all items to the card's collection in one batch
            if (perVehicleAccessList.Any())
            {
                card.PerVehicleNormalCardAccessItems.AddRange(perVehicleAccessList);
            }

            return perVehicleAccessList;
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var departments = (await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true)).Select(a => a.DepartmentId).ToList();

            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, $"ModelId == @0 and @1.Contains(outerIt.DepartmentId)", new object[] { modelId, departments }, skipSecurity: true);

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var vehicle in vehicles)
            {
                if (!card.PerVehicleNormalCardAccessItems.Where(a => a.VehicleId == vehicle.Id && a.PermissionId == a.PermissionId).Any())
                {
                    var perVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    perVehicleAccess.VehicleId = vehicle.Id;
                    perVehicleAccess.PermissionId = permissionId;

                    card.PerVehicleNormalCardAccessItems.Add(perVehicleAccess);
                    perVehicleAccessList.Add(perVehicleAccess);
                }
            }

            return perVehicleAccessList;
        }

        private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                                                List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.SiteId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToSiteVehicleNormalAccessViewItemsAsync();

            // Add any existing accesses that are not in updates to toRemove
            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    var viewItem = person.PersonToSiteVehicleNormalAccessViewItems.FirstOrDefault(x =>
                        x.SiteId == existing.Key.SiteId &&
                        x.PermissionId == existing.Key.PermissionId);

                    if (viewItem != null)
                    {
                        viewItem.HasAccess = false;
                        toRemove.Add(viewItem);
                    }
                }
            }

            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToDepartmentVehicleNormalAccessViewItemsAsync();

            // Add any existing accesses that are not in updates to toRemove
            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    var viewItem = person.PersonToDepartmentVehicleNormalAccessViewItems.FirstOrDefault(x =>
                        x.DepartmentId == existing.Key.DepartmentId &&
                        x.PermissionId == existing.Key.PermissionId);

                    if (viewItem != null)
                    {
                        viewItem.HasAccess = false;
                        toRemove.Add(viewItem);
                    }
                }
            }

            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToModelVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToModelVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToModelVehicleNormalAccessViewDataObject>();

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid ModelId, Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.ModelId, update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToModelVehicleNormalAccessViewItemsAsync();

            // Add any existing accesses that are not in updates to toRemove
            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    var viewItem = person.PersonToModelVehicleNormalAccessViewItems.FirstOrDefault(x =>
                        x.ModelId == existing.Key.ModelId &&
                        x.DepartmentId == existing.Key.DepartmentId &&
                        x.PermissionId == existing.Key.PermissionId);

                    if (viewItem != null)
                    {
                        viewItem.HasAccess = false;
                        toRemove.Add(viewItem);
                    }
                }
            }

            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToPerVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToPerVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToPerVehicleNormalAccessViewDataObject>();

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid VehicleId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.VehicleId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToPerVehicleNormalAccessViewItemsAsync();

            // Add any existing accesses that are not in updates to toRemove
            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    var viewItem = person.PersonToPerVehicleNormalAccessViewItems.FirstOrDefault(x =>
                        x.VehicleId == existing.Key.VehicleId &&
                        x.PermissionId == existing.Key.PermissionId);

                    if (viewItem != null)
                    {
                        viewItem.HasAccess = false;
                        toRemove.Add(viewItem);
                    }
                }
            }

            return (toAdd, toRemove);
        }

        private DepartmentVehicleNormalCardAccessDataObject CreateDepartmentAccess(
            Guid departmentId,
            Guid permissionId)
        {
            var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
            deptAccess.DepartmentId = departmentId;
            deptAccess.PermissionId = permissionId;
            return deptAccess;
        }

        private ModelVehicleNormalCardAccessDataObject CreateModelAccess(
            Guid modelId,
            Guid departmentId,
            Guid permissionId)
        {
            var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
            modelAccess.ModelId = modelId;
            modelAccess.DepartmentId = departmentId;
            modelAccess.PermissionId = permissionId;
            return modelAccess;
        }

        private SiteVehicleNormalCardAccessDataObject CreateSiteAccess(Guid siteId, Guid permissionId)
        {
            var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
            siteAccess.SiteId = siteId;
            siteAccess.PermissionId = permissionId;
            return siteAccess;
        }
        private PerVehicleNormalCardAccessDataObject CreateVehicleAccess(
            Guid vehicleId,
            Guid permissionId)
        {
            var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            vehicleAccess.VehicleId = vehicleId;
            vehicleAccess.PermissionId = permissionId;
            return vehicleAccess;
        }

        private async Task<DepartmentDataObject> GetDepartmentAsync(Guid departmentId)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = departmentId;
            return await _dataFacade.DepartmentDataProvider.GetAsync(department, skipSecurity: true)
                ?? throw new GOServerException($"Department {departmentId} not found");
        }
        private async System.Threading.Tasks.Task<PersonDataObject> GetPersonAsync(Guid personId)
        {
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = personId;

            person = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> {
                "Driver.Card",
                    "Driver.Card.SiteVehicleNormalCardAccessItems",
                    "Driver.Card.DepartmentVehicleNormalCardAccessItems",
                    "Driver.Card.ModelVehicleNormalCardAccessItems",
                    "Driver.Card.PerVehicleNormalCardAccessItems" }, skipSecurity: true);

            if (person == null || person.Driver == null || person.Driver.Card == null)
            {
                throw new GOServerException("Something went wrong. Input should be a person who is a driver and has an access card. One of those is not happening");
            }

            return person;
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Load all department access items first
            var deptAccessItems = await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter by permissionId first to reduce unnecessary department loads
            var relevantAccesses = deptAccessItems
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return Enumerable.Empty<PerVehicleNormalCardAccessDataObject>();
            }

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            // Process relevant accesses sequentially
            foreach (var deptAccess in relevantAccesses)
            {
                var department = await deptAccess.LoadDepartmentAsync(skipSecurity: true);

                if (department.SiteId == siteId)
                {
                    // Mark for deletion
                    deptAccess.IsMarkedForDeletion = true;

                    // Remove vehicle access first as it depends on department existence
                    var perVehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                        card,
                        deptAccess.DepartmentId,
                        permissionId);

                    if (perVehicleAccesses.Any())
                    {
                        perVehicleAccessList.AddRange(perVehicleAccesses);
                    }

                    // Remove model access last as it might reference vehicles
                    await RemoveAccessForModelsOfDepartmentAsync(
                        card,
                        department,
                        permissionId);
                }
            }

            return perVehicleAccessList;
        }
        private async Task RemoveAccessForModelsOfDepartmentAsync(
            CardDataObject card,
            DepartmentDataObject department,
            Guid permissionId)
        {
            // Load data sequentially to avoid MARS issues
            var site = await department.LoadSiteAsync();
            await site.LoadDepartmentItemsAsync(skipSecurity: true);

            // Get current department's models
            var currentDeptVehicles = await department.LoadVehiclesAsync(skipSecurity: true);
            var currentDeptModelIds = currentDeptVehicles
                .Select(v => v.ModelId)
                .ToHashSet();

            // Load other departments' vehicles sequentially
            var otherDeptModelIds = new HashSet<Guid>();
            foreach (var otherDept in site.DepartmentItems.Where(d => d.Id != department.Id))
            {
                var vehicles = await otherDept.LoadVehiclesAsync(skipSecurity: true);
                foreach (var vehicle in vehicles)
                {
                    otherDeptModelIds.Add(vehicle.ModelId);
                }
            }

            // Find models unique to current department
            var uniqueModelIds = currentDeptModelIds.Except(otherDeptModelIds);

            // Create lookup for existing access items
            var existingAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
                .ToLookup(a => a.ModelId);

            // Mark items for deletion
            foreach (var modelId in uniqueModelIds)
            {
                var access = existingAccesses[modelId].SingleOrDefault();
                if (access != null)
                {
                    access.IsMarkedForDeletion = true;
                }
            }
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfDepartmentAsync(
            CardDataObject card,
            Guid departmentId,
            Guid permissionId)
        {
            // Load all vehicle access items in one go
            var perVehicleAccessList = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter to only relevant items based on permissionId first
            var relevantAccesses = perVehicleAccessList
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return perVehicleAccessList;
            }

            // Load vehicles sequentially to avoid MARS issues
            foreach (var access in relevantAccesses)
            {
                var vehicle = await access.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.DepartmentId == departmentId)
                {
                    access.IsMarkedForDeletion = true;
                }
            }

            return perVehicleAccessList;
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var allAccessItems = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>(); // Assuming AccessItem is the type of the items in the collection

            foreach (var accessItem in allAccessItems)
            {
                var vehicle = await accessItem.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.ModelId == modelId && accessItem.PermissionId == permissionId)
                {
                    perVehicleAccessList.Add(accessItem);
                }
            }

            foreach (var perVehicleAccess in perVehicleAccessList)
            {
                perVehicleAccess.IsMarkedForDeletion = true;
            }

            return perVehicleAccessList;
        }

        private async Task SyncDriversInScopeAsync(List<Guid> vehicleIds, IDataFacade scopedDataFacade, IDeviceTwinHandler scopedDeviceTwinHandler, IServiceProvider serviceProvider, Guid? userId = null)
        {
            _logger?.LogInformation($"[PERF] SyncDriversInScopeAsync called with {vehicleIds.Count} vehicle IDs");
            if (vehicleIds.Any())
            {
                var iotDevices = new List<string>();

                if (userId == null)
                {
                    return;
                }

                foreach (var vehicleId in vehicleIds)
                {
                    try
                    {
                        // Reload vehicle data in the new scope
                        var vehicle = serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = vehicleId;
                        vehicle = await scopedDataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);

                        if (vehicle != null)
                        {
                            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                            if (module != null && !string.IsNullOrEmpty(module.IoTDevice))
                            {
                                if (!iotDevices.Contains(module.IoTDevice))
                                {
                                    iotDevices.Add(module.IoTDevice);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"Failed to load vehicle {vehicleId} for sync: {ex.Message}");
                    }
                }

                if (iotDevices.Any())
                {
                    foreach (var iotDevice in iotDevices)
                    {
                        try
                        {
                            _logger?.LogInformation($"Syncing driver to vehicle {iotDevice} for user {userId}");
                            await scopedDeviceTwinHandler.SyncDriverToVehicle(iotDevice, userId.Value);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, $"Failed to sync device {iotDevice}: {ex.Message}");
                        }
                    }
                }
            }
        }
    }
}
