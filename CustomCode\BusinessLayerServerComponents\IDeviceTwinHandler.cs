using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public partial interface IDeviceTwinHandler
    {
        Task SyncOnDemandSettingAndUsersAsync(string deviceId);
        Task SyncDriverToVehicle(string deviceId, Guid? userId = null);
        Task SyncChecklistToVehicle(string deviceId);
        Task SyncChecklistSettingToVehicle(string deviceId);
        Task SyncTimezone(string deviceId);
        Task SyncGeneralSettings(string deviceId);
        Task UpdateFirmware(string deviceId, String FirmwareVersion);
        Task UpdateCANRuleAsync(string deviceId, String canModCrc);
        Task SyncVORSettingsAsync(string deviceId);
        Task SendWebVORUpdateToVehicleAsync(string deviceId, bool? status);
        Task<string> PrepareMultiLanguageChecklistAsync(string deviceId);
        Task SyncFullLockOutSettingsAsync(string deviceId);
        Task SyncLogoAsync(string deviceId, string logoUrl);
    }
}
