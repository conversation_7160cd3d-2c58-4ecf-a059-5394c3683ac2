const fs = require('fs-extra');
const path = require('path');

async function findMermaidFiles(dir, sourceDir) {
    let files = [];
    const items = await fs.readdir(dir);
    for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = await fs.stat(fullPath);
        if (stat.isDirectory()) {
            // Skip node_modules and .vuepress directories
            if (item !== 'node_modules' && item !== '.vuepress') {
                files = files.concat(await findMermaidFiles(fullPath, sourceDir));
            }
        } else if (item.endsWith('.mermaid')) {
            files.push(path.relative(sourceDir, fullPath));
        }
    }
    return files;
}

module.exports = (options, ctx) => {
    return {
        name: 'vuepress-plugin-mermaid-file-pages',

        async additionalPages() {
            const { sourceDir } = ctx;
            const mermaidFiles = await findMermaidFiles(sourceDir, sourceDir);

            const pages = [];
            for (const relativePath of mermaidFiles) {
                const absolutePath = path.join(sourceDir, relativePath);
                const content = await fs.readFile(absolutePath, 'utf-8');

                const pageContent = `---
title: ${path.basename(relativePath, '.mermaid').replace(/[_-]/g, ' ')}
---

# ${path.basename(relativePath, '.mermaid').replace(/[_-]/g, ' ')}

**Source:** \`${relativePath}\`

## Diagram

\`\`\`mermaid
${content}
\`\`\`

## Diagram Details

- **File:** ${relativePath}
- **Size:** ${content.length} characters
- **Last Updated:** ${new Date().toLocaleDateString()}

## Raw Mermaid Code

\`\`\`mermaid
${content}
\`\`\`
`;

                const pageRelativePath = relativePath.replace(/\.mermaid$/, '.html');

                pages.push({
                    content: pageContent,
                    path: `/${pageRelativePath}`,
                    frontmatter: {
                        title: path.basename(relativePath, '.mermaid').replace(/[_-]/g, ' ')
                    }
                });
            }
            return pages;
        },

        extendMarkdown(md) {
            const defaultRender = md.renderer.rules.fence || function (tokens, idx, options, env, self) {
                return self.renderToken(tokens, idx, options);
            };

            md.renderer.rules.fence = function (tokens, idx, options, env, self) {
                const token = tokens[idx];
                const lang = token.info.trim();

                if (lang.startsWith('mermaid-file=')) {
                    const filePath = lang.substring('mermaid-file='.length);
                    try {
                        const absolutePath = path.join(ctx.sourceDir, filePath);
                        const mermaidContent = fs.readFileSync(absolutePath, 'utf-8').trim();

                        return `
                            <div class="mermaid-file-container">
                                <div class="mermaid">
${mermaidContent}
                                </div>
                            </div>
                        `;
                    } catch (error) {
                        return `
                            <div class="mermaid-file-container">
                                <div style="color: red; padding: 10px; border: 1px solid red;">
                                    Error loading diagram from ${filePath}: ${error.message}
                                </div>
                            </div>
                        `;
                    }
                }

                return defaultRender(tokens, idx, options, env, self);
            };
        }
    };
}; 