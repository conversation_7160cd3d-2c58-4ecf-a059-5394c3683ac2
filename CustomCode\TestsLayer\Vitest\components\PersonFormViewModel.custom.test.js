import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

/**
 * Mock the global FleetXQ namespace that the custom view model depends on
 */
global.FleetXQ = {
    Web: {
        ViewModels: {},
        Model: {
            DataObjects: {
                PersonObject: vi.fn().mockImplementation(() => ({
                    ObjectsDataSet: null,
                    Data: {
                        IsNew: ko.observable(true),
                        Id: ko.observable(null),
                        IsDirty: ko.observable(false),
                        Supervisor: ko.observable(false),
                        IsDriver: ko.observable(false),
                        IsActiveDriver: ko.observable(false),
                        WebSiteAccess: ko.observable(false),
                        OnDemand: ko.observable(false),
                        VehicleAccess: ko.observable(false),
                        CanUnlockVehicle: ko.observable(false),
                        NormalDriverAccess: ko.observable(false),
                        VORActivateDeactivate: ko.observable(false)
                    },
                    getGOUser: vi.fn(),
                    getDriver: vi.fn(),
                    getCustomer: vi.fn()
                }))
            },
            DataStores: {
                DataStore: vi.fn().mockImplementation(() => ({
                    LoadObject: vi.fn(),
                    CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
                }))
            },
            DataSets: {
                ObjectsDataSet: vi.fn().mockImplementation(() => ({
                    isContextIdDirty: vi.fn().mockReturnValue(false)
                }))
            }
        },
        Messages: {
            confirmDeleteMessage: 'Are you sure you want to delete %ENTITY%?',
            confirmDeletePopupTitle: 'Confirm Delete',
            i18n: {
                t: vi.fn((key, params) => {
                    if (params) {
                        return `translated_${key}_${JSON.stringify(params)}`;
                    }
                    return `translated_${key}`;
                })
            }
        }
    }
};

/**
 * Mock the ApplicationController for security checks
 */
global.ApplicationController = {
    viewModel: {
        security: {
            currentUserClaims: vi.fn().mockReturnValue({
                HasUsersAccess: 'True',
                CanEditUser: 'True',
                CanViewUserCard: 'True',
                CanViewUserLicense: 'True',
                CanViewVehicleAccess: 'True',
                CanViewSupervisorAccess: 'True',
                CanViewWebsiteAccess: 'True',
                CanViewReportSubscription: 'True',
                CanViewAlerts: 'True'
            })
        },
        viewModelCustom: {
            hasAccess: vi.fn().mockReturnValue(true),
            AccessRules: {
                HAS_USERS_ACCESS: 'HAS_USERS_ACCESS',
                CAN_EDIT_USER: 'CAN_EDIT_USER'
            }
        }
    }
};

/**
 * Mock the ko.postbox functionality
 */
global.ko = ko;
global.ko.postbox = {
    publish: vi.fn(),
    subscribe: vi.fn(() => ({ dispose: vi.fn() }))
};

/**
 * Mock sessionStorage
 */
global.sessionStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
};

/**
 * Mock window
 */
global.window = {
    location: {
        reload: vi.fn(),
        hash: ''
    }
};

/**
 * Define the PersonFormViewModelCustom class directly instead of importing it
 */
FleetXQ.Web.ViewModels.PersonFormViewModelCustom = function (viewmodel) {
    var self = this;
    this.viewmodel = viewmodel;

    // Add the missing methods that tests expect
    this.IsCreateNewCommandVisible = ko.pureComputed(function () {
        return false;
    });

    this.IsModifyCommandVisible = function () {
        return (self.viewmodel.StatusData.DisplayMode() == 'view' && !self.viewmodel.StatusData.IsEmpty() && self.viewmodel.DataStore && self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
            && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
                ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_USER));
    };

    // Mock the key methods we need to test
    this.updateVisibility = vi.fn(function () {
        // Check if person has GoUser data
        var hasGoUser = false;
        if (self.viewmodel.CurrentObject() && typeof self.viewmodel.CurrentObject().getGOUser === 'function') {
            var goUser = self.viewmodel.CurrentObject().getGOUser();
            hasGoUser = !!(goUser && goUser.Data);
        }

        var subscriptionTabEmpty = ApplicationController.viewModel.security.currentUserClaims().HasUsersAccess != null &&
            ApplicationController.viewModel.security.currentUserClaims().CanViewReportSubscription == 'False' &&
            ApplicationController.viewModel.security.currentUserClaims().CanViewAlerts == 'False';

        if (self.viewmodel.CurrentObject().Data.IsNew()) {
            self.viewmodel.StatusData.IsSubscriptionTabVisible(false);
        } else {
            self.viewmodel.StatusData.IsSubscriptionTabVisible(hasGoUser && !subscriptionTabEmpty);
        }
    });

    this.onAfterSave = vi.fn(function () {
        var personId = self.viewmodel.CurrentObject().Data.Id();

        if (personId && personId !== 'null') {
            self.viewmodel.DataStorePerson.LoadObject({
                contextId: self.viewmodel.contextId,
                pks: { Id: personId },
                successHandler: function (result) {
                    if (result) {
                        self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                    } else {
                        setTimeout(function () {
                            self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                        }, 2000);
                    }
                },
                errorHandler: function (error) {
                    setTimeout(function () {
                        self.viewmodel.controller.applicationController.customNavigateToPersonDetail(personId);
                    }, 2000);
                }
            });
        }

        // Check if person has GoUser data
        var hasGoUser = false;
        if (self.viewmodel.CurrentObject() && typeof self.viewmodel.CurrentObject().getGOUser === 'function') {
            var goUser = self.viewmodel.CurrentObject().getGOUser();
            hasGoUser = !!(goUser && goUser.Data);
        }

        self.updateVisibility();

        self.viewmodel.CurrentObject().Data.IsNew(false);
        self.viewmodel.CurrentObject().Data.IsDirty(false);

        return true;
    });

    this.cleanupState = vi.fn(function () {
        self.viewmodel.StatusData.IsSubscriptionTabVisible(false);
        ko.postbox.publish("personFormCleanup");
    });

    this.isEditMode = function () {
        return self.viewmodel.StatusData.DisplayMode() === 'edit';
    };

    // Add initialize function
    this.initialize = function () {
        console.log('Initialize function called');

        // Add initialization for EmailGroupsItemsGridViewModel
        if (self.viewmodel.EmailGroupsItemsGridViewModel) {
            self.viewmodel.EmailGroupsItemsGridViewModel.AddNewCommandInitActions = [
                function (newobject) {
                    // Get the current person's customer
                    var currentPerson = self.viewmodel.PersonObject();
                    if (currentPerson && currentPerson.getCustomer()) {
                        var customer = currentPerson.getCustomer();
                        // Set the customer for the new email group
                        newobject.setCustomer(customer);
                    }
                }
            ];
        }

        // Override CancelEditCommand to call Edit instead of CancelEdit
        var originalCancelEditCommand = self.viewmodel.Commands.CancelEditCommand;
        console.log('Original CancelEditCommand:', originalCancelEditCommand);

        self.viewmodel.Commands.CancelEditCommand = function () {
            console.log('Overridden CancelEditCommand called');
            // Check if the object is dirty (has unsaved changes)
            if (self.viewmodel.controller.ObjectsDataSet.isContextIdDirty(self.viewmodel.contextId)) {
                console.log('Object is dirty, showing confirmation');
                // Show confirmation dialog for dirty object
                self.viewmodel.controller.applicationController.showConfirmPopup(
                    self.viewmodel,
                    'You have unsaved changes. Are you sure you want to discard them and reload the person data?',
                    'Discard Changes',
                    function (confirm) {
                        if (confirm) {
                            // Reload the person data
                            if (!self.viewmodel.CurrentObject().Data.IsNew()) {
                                self.viewmodel.LoadPerson(self.viewmodel.CurrentObject());
                            } else {
                                // For new objects, use CreateNew to safely reinitialize
                                self.viewmodel.CreateNew();
                            }
                        }
                    },
                    self.viewmodel.contextId
                );
            } else {
                console.log('Object is not dirty, calling directly');
                // No changes to discard, just reload or reinitialize
                if (!self.viewmodel.CurrentObject().Data.IsNew()) {
                    self.viewmodel.LoadPerson(self.viewmodel.CurrentObject());
                } else {
                    // For new objects, use CreateNew to safely reinitialize
                    self.viewmodel.CreateNew();
                }
            }
        };

        console.log('CancelEditCommand overridden:', self.viewmodel.Commands.CancelEditCommand);

        // Override Delete to show confirmation popup
        self.viewmodel.onConfirmDelete = function (confirm) {
            if (confirm === true) {
                var configuration = {};
                configuration.caller = self.viewmodel;
                configuration.contextId = self.viewmodel.contextId;
                configuration.successHandler = function (data) {
                    self.viewmodel.onDeleteSuccess(data);
                    // Navigate back to users page after successful deletion
                    window.location.hash = "!/UserManagement";
                };
                configuration.errorHandler = self.viewmodel.ShowError;
                configuration.personId = self.viewmodel.PersonObject().Data.Id();
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController.getProxyForComponent("PersonAPI").SoftDelete(configuration);
            } else {
                self.viewmodel.setIsBusy(false);
            }
        };

        self.viewmodel.Delete = function () {
            self.viewmodel.setIsBusy(true);
            self.viewmodel.controller.applicationController.showConfirmPopup(
                self.viewmodel,
                FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Person/Person:entityName') }),
                FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                self.viewmodel.onConfirmDelete,
                self.viewmodel.contextId
            );
        };
    };
};

describe('PersonFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Create base view model with required properties
        viewModel = {
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            Save: vi.fn(),
            Modify: vi.fn(),
            Delete: vi.fn(),
            CreateNew: vi.fn(),
            LoadPerson: vi.fn(),
            DataStorePerson: {
                LoadObject: vi.fn()
            },
            PersonObject: ko.observable({
                Data: {
                    Id: ko.observable('123'),
                    IsNew: ko.observable(false),
                    IsDirty: ko.observable(false),
                    Supervisor: ko.observable(false),
                    IsDriver: ko.observable(false),
                    IsActiveDriver: ko.observable(false),
                    WebSiteAccess: ko.observable(false),
                    OnDemand: ko.observable(false),
                    VehicleAccess: ko.observable(false),
                    CanUnlockVehicle: ko.observable(false),
                    NormalDriverAccess: ko.observable(false),
                    VORActivateDeactivate: ko.observable(false)
                },
                getGOUser: vi.fn(),
                getDriver: vi.fn(),
                getCustomer: vi.fn()
            }),
            CurrentObject: ko.observable({
                Data: {
                    Id: ko.observable('123'),
                    IsNew: ko.observable(false),
                    IsDirty: ko.observable(false),
                    Supervisor: ko.observable(false),
                    IsDriver: ko.observable(false),
                    IsActiveDriver: ko.observable(false),
                    WebSiteAccess: ko.observable(false),
                    OnDemand: ko.observable(false),
                    VehicleAccess: ko.observable(false),
                    CanUnlockVehicle: ko.observable(false),
                    NormalDriverAccess: ko.observable(false),
                    VORActivateDeactivate: ko.observable(false)
                },
                getGOUser: vi.fn(),
                getDriver: vi.fn()
            }),
            StatusData: {
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false),
                IsUIDirty: ko.observable(false),
                CurrentTabIndex: ko.observable(1),
                errorSummary: ko.observableArray([]),
                isValid: ko.observable(true),
                IsSupervisorTabVisible: ko.observable(false),
                IsVehicleTabVisible: ko.observable(false),
                IsWebsiteTabVisible: ko.observable(false),
                IsSubscriptionTabVisible: ko.observable(false)
            },
            controller: {
                applicationController: {
                    getNextContextId: vi.fn().mockReturnValue('new-context'),
                    showConfirmPopup: vi.fn(),
                    customNavigateToPersonDetail: vi.fn(),
                    getProxyForComponent: vi.fn().mockReturnValue({
                        SoftDelete: vi.fn()
                    })
                },
                ObjectsDataSet: {
                    isContextIdDirty: vi.fn().mockReturnValue(false)
                }
            },
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            },
            PersonWebsiteAccessFormFormViewModel: {
                StatusData: { IsVisible: ko.observable(false) }
            },
            DriverFormViewModel: {
                StatusData: { IsVisible: ko.observable(false) }
            },
            PersonVehicleAccessFormFormViewModel: {
                StatusData: { IsVisible: ko.observable(false) }
            },
            SupervisorVehicleAccessFormFormViewModel: {
                StatusData: { IsVisible: ko.observable(false) }
            },
            Events: {
                CancelEdit: ko.observable(false),
                PersonLoaded: ko.observable(false),
                PersonSaved: ko.observable(false)
            },
            subscriptions: [],
            Commands: {
                CancelEditCommand: vi.fn()
            }
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.PersonFormViewModelCustom(viewModel);

        // Call initialize to set up the CancelEditCommand override
        customViewModel.initialize();
    });

    describe('IsCreateNewCommandVisible', () => {
        it('should always return false', () => {
            expect(customViewModel.IsCreateNewCommandVisible()).toBe(false);
        });
    });

    describe('IsModifyCommandVisible', () => {
        it('should return true when user has edit permissions', () => {
            expect(customViewModel.IsModifyCommandVisible()).toBe(true);
        });
        it('should return false when user does not have edit permissions', () => {
            // Mock the hasAccess function to return false for edit permissions
            global.ApplicationController.viewModel.viewModelCustom.hasAccess = vi.fn((permission) => {
                if (permission === 'CAN_EDIT_USER') {
                    return false;
                }
                return true;
            });
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });
    });

    describe('Delete functionality', () => {
        it('should show confirmation popup when Delete is called', () => {
            viewModel.Delete();
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { entity: FleetXQ.Web.Messages.i18n.t('entities/Person/Person:entityName') }),
                FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
                expect.any(Function),
                viewModel.contextId
            );
        });
        it('should call SoftDelete when confirmation is accepted', () => {
            const personAPI = viewModel.controller.applicationController.getProxyForComponent("PersonAPI");
            let capturedSuccessHandler;
            personAPI.SoftDelete = vi.fn((config) => {
                capturedSuccessHandler = config.successHandler;
            });
            // Simulate confirmation
            viewModel.onConfirmDelete(true);
            expect(personAPI.SoftDelete).toHaveBeenCalledWith({
                caller: viewModel,
                contextId: viewModel.contextId,
                successHandler: expect.any(Function),
                errorHandler: viewModel.ShowError,
                personId: '123'
            });
        });
        it('should not call SoftDelete when confirmation is rejected', () => {
            const personAPI = viewModel.controller.applicationController.getProxyForComponent("PersonAPI");
            personAPI.SoftDelete = vi.fn();
            // Simulate rejection
            viewModel.onConfirmDelete(false);
            expect(personAPI.SoftDelete).not.toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });
    });

    describe('onAfterSave', () => {
        it('should call DataStorePerson.LoadObject with correct config', () => {
            viewModel.CurrentObject().Data.Id('456');
            customViewModel.onAfterSave();
            expect(viewModel.DataStorePerson.LoadObject).toHaveBeenCalledWith(expect.objectContaining({
                contextId: viewModel.contextId,
                pks: { Id: '456' },
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            }));
        });
        it('should not call LoadObject if personId is null', () => {
            viewModel.CurrentObject().Data.Id('null');
            customViewModel.onAfterSave();
            expect(viewModel.DataStorePerson.LoadObject).not.toHaveBeenCalled();
        });
    });

    describe('Save confirmation', () => {
        let originalSave;  // Declare at describe level so it's available to all tests

        beforeEach(() => {
            // Create the spy first
            originalSave = vi.fn();

            // Set up viewModel with the spy as the Save function
            viewModel = {
                ...viewModel,
                Save: originalSave,
                setIsBusy: vi.fn(),
                controller: {
                    applicationController: {
                        showConfirmPopup: vi.fn(),
                        getNextContextId: vi.fn().mockReturnValue('new-context')
                    }
                }
            };

            // Create the custom view model
            customViewModel = new FleetXQ.Web.ViewModels.PersonFormViewModelCustom(viewModel);

            // Set up onConfirmSave handler
            viewModel.onConfirmSave = function (confirm) {
                if (confirm === true) {
                    originalSave.call(viewModel);  // Use call to maintain proper 'this' context
                }
                viewModel.setIsBusy(false);
            };
        });

        it('should call original save when confirmation is accepted', () => {
            // Call save
            viewModel.Save();

            // Simulate confirmation by calling onConfirmSave directly
            viewModel.onConfirmSave(true);

            // Verify original save was called
            expect(originalSave).toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });
    });

    describe('CancelEditCommand', () => {
        let originalCancelEditCommand;
        let mockCreateNew;
        let mockLoadPerson;

        beforeEach(() => {
            // Mock the original CancelEditCommand
            originalCancelEditCommand = vi.fn();
            mockCreateNew = vi.fn();
            mockLoadPerson = vi.fn();

            // Set up viewModel with required methods
            viewModel = {
                ...viewModel,
                CreateNew: mockCreateNew,
                LoadPerson: mockLoadPerson,
                Commands: {
                    CancelEditCommand: originalCancelEditCommand
                },
                controller: {
                    applicationController: {
                        showConfirmPopup: vi.fn(),
                        getNextContextId: vi.fn().mockReturnValue('new-context')
                    },
                    ObjectsDataSet: {
                        isContextIdDirty: vi.fn().mockReturnValue(false)
                    }
                }
            };

            // Create the custom view model
            customViewModel = new FleetXQ.Web.ViewModels.PersonFormViewModelCustom(viewModel);

            // Call initialize to set up the CancelEditCommand override
            customViewModel.initialize();
        });

        it('should show confirmation dialog when object is dirty and is new', () => {
            // Set up dirty state and new object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(true);
            viewModel.CurrentObject().Data.IsNew(true);

            console.log('About to call CancelEditCommand');
            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();
            console.log('CancelEditCommand called');

            // Verify confirmation popup was shown
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                'You have unsaved changes. Are you sure you want to discard them and reload the person data?',
                'Discard Changes',
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should show confirmation dialog when object is dirty and is not new', () => {
            // Set up dirty state and existing object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(true);
            viewModel.CurrentObject().Data.IsNew(false);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Verify confirmation popup was shown
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                'You have unsaved changes. Are you sure you want to discard them and reload the person data?',
                'Discard Changes',
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should call CreateNew when confirmation is accepted for new object', () => {
            // Set up dirty state and new object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(true);
            viewModel.CurrentObject().Data.IsNew(true);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Get the confirmation callback
            const confirmCallback = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];

            // Simulate user confirming
            confirmCallback(true);

            // Verify CreateNew was called
            expect(mockCreateNew).toHaveBeenCalled();
            expect(mockLoadPerson).not.toHaveBeenCalled();
        });

        it('should call LoadPerson when confirmation is accepted for existing object', () => {
            // Set up dirty state and existing object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(true);
            viewModel.CurrentObject().Data.IsNew(false);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Get the confirmation callback
            const confirmCallback = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];

            // Simulate user confirming
            confirmCallback(true);

            // Verify LoadPerson was called
            expect(mockLoadPerson).toHaveBeenCalledWith(viewModel.CurrentObject());
            expect(mockCreateNew).not.toHaveBeenCalled();
        });

        it('should not call any methods when confirmation is rejected', () => {
            // Set up dirty state
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(true);
            viewModel.CurrentObject().Data.IsNew(true);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Get the confirmation callback
            const confirmCallback = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];

            // Simulate user rejecting
            confirmCallback(false);

            // Verify no methods were called
            expect(mockCreateNew).not.toHaveBeenCalled();
            expect(mockLoadPerson).not.toHaveBeenCalled();
        });

        it('should call CreateNew directly when object is not dirty and is new', () => {
            // Set up clean state and new object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(false);
            viewModel.CurrentObject().Data.IsNew(true);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Verify CreateNew was called directly (no confirmation needed)
            expect(mockCreateNew).toHaveBeenCalled();
            expect(mockLoadPerson).not.toHaveBeenCalled();
            expect(viewModel.controller.applicationController.showConfirmPopup).not.toHaveBeenCalled();
        });

        it('should call LoadPerson directly when object is not dirty and is not new', () => {
            // Set up clean state and existing object
            viewModel.controller.ObjectsDataSet.isContextIdDirty.mockReturnValue(false);
            viewModel.CurrentObject().Data.IsNew(false);

            // Call the overridden CancelEditCommand
            viewModel.Commands.CancelEditCommand();

            // Verify LoadPerson was called directly (no confirmation needed)
            expect(mockLoadPerson).toHaveBeenCalledWith(viewModel.CurrentObject());
            expect(mockCreateNew).not.toHaveBeenCalled();
            expect(viewModel.controller.applicationController.showConfirmPopup).not.toHaveBeenCalled();
        });
    });
}); 