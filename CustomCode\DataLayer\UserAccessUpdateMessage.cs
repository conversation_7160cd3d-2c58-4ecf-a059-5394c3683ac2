using System;
using System.Collections.Generic;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Data.DataObjects
{
    /// <summary>
    /// Message class for user access update operations sent to Azure Service Bus
    /// Contains all the necessary data to process user access updates asynchronously
    /// </summary>
    public class UserAccessUpdateMessage
    {
        /// <summary>
        /// The person ID for whom access is being updated
        /// </summary>
        public Guid PersonId { get; set; }

        /// <summary>
        /// Customer ID for multi-tenant isolation
        /// </summary>
        public Guid CustomerId { get; set; }

        /// <summary>
        /// Site vehicle access updates (serialized as JSON)
        /// </summary>
        public string PersonToSiteAccessesJson { get; set; }

        /// <summary>
        /// Department vehicle access updates (serialized as JSO<PERSON>)
        /// </summary>
        public string PersonToDepartmentAccessesJson { get; set; }

        /// <summary>
        /// Model vehicle access updates (serialized as JSON)
        /// </summary>
        public string PersonToModelAccessesJson { get; set; }

        /// <summary>
        /// Per-vehicle access updates (serialized as J<PERSON><PERSON>)
        /// </summary>
        public string PersonToVehicleAccessesJ<PERSON> { get; set; }

        /// <summary>
        /// Timestamp when the message was created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// User ID who initiated the access update
        /// </summary>
        public Guid? InitiatedByUserId { get; set; }

        /// <summary>
        /// Optional correlation ID for tracking related operations
        /// </summary>
        public string CorrelationId { get; set; }

        /// <summary>
        /// Priority level for processing (Normal, High, Critical)
        /// </summary>
        public string Priority { get; set; } = "Normal";
    }
}