sequenceDiagram
    participant User
    participant App
    participant Router
    participant Layout
    participant Navigation
    participant Page
    participant Component
    participant API

    User->>App: Access Application
    App->>Router: Initialize Router
    Router->>Layout: Load Layout
    Layout->>Navigation: Render Navigation
    Navigation->>Page: Navigate to Page
    Page->>Component: Load Components
    Component->>API: Fetch Data
    API-->>Component: Data Response
    Component-->>Page: Render Data
    Page-->>Layout: Display Page
    Layout-->>App: Complete Render
    App-->>User: Show Application