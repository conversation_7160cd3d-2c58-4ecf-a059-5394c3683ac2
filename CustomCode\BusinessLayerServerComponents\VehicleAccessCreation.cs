﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Text.Json;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// VehicleAccessCreation Component
	///  
	/// </summary>
    public partial class VehicleAccessCreation : BaseServerComponent, IVehicleAccessCreation
    {
        private readonly ILogger<VehicleAccessCreation> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public VehicleAccessCreation(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, ILogger<VehicleAccessCreation> logger, IServiceScopeFactory serviceScopeFactory) : base(serviceProvider, configuration, dataFacade)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
        }

        /// <summary>
        /// CreateVehicleAccess Method
        /// </summary>
        /// <param name="Message"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> CreateVehicleAccessAsync(System.String Message, Dictionary<string, object> parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var vehicleId = Guid.Empty;

            try
            {
                _logger.LogInformation("[PERF] Starting vehicle access creation process");

                // Parse the JSON message to get vehicle information
                var parseStart = Stopwatch.StartNew();
                var vehicleAccessMessage = System.Text.Json.JsonSerializer.Deserialize<VehicleAccessCreationMessage>(Message);
                parseStart.Stop();
                _logger.LogDebug("[PERF] Message parsing completed in {ElapsedMs}ms", parseStart.ElapsedMilliseconds);

                if (vehicleAccessMessage == null)
                {
                    _logger.LogWarning("[PERF] Invalid message format received");
                    return new ComponentResponse<System.String>("Invalid message format");
                }

                vehicleId = vehicleAccessMessage.VehicleId;
                var processType = vehicleAccessMessage.IsDepartmentChange ? "department change" :
                                 vehicleAccessMessage.IsNewVehicle ? "new vehicle" : "vehicle access update";
                _logger.LogInformation("[PERF] Processing {ProcessType} for VehicleId: {VehicleId}", processType, vehicleId);

                // Load the vehicle from the database
                var vehicleLoadStart = Stopwatch.StartNew();
                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                vehicle.Id = vehicleAccessMessage.VehicleId;
                vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);
                vehicleLoadStart.Stop();
                _logger.LogDebug("[PERF] Vehicle load completed in {ElapsedMs}ms", vehicleLoadStart.ElapsedMilliseconds);

                if (vehicle == null)
                {
                    _logger.LogWarning("[PERF] Vehicle {VehicleId} not found", vehicleId);
                    return new ComponentResponse<System.String>("Vehicle not found");
                }

                // Load required entities sequentially to avoid session conflicts
                var entitiesLoadStart = Stopwatch.StartNew();
                var site = await vehicle.LoadSiteAsync(skipSecurity: true);
                var model = await vehicle.LoadModelAsync(skipSecurity: true);
                entitiesLoadStart.Stop();
                _logger.LogInformation("[PERF] Site and model loading completed in {ElapsedMs}ms", entitiesLoadStart.ElapsedMilliseconds);

                if (site == null || model == null)
                {
                    _logger.LogWarning("[PERF] Site or model not found for VehicleId: {VehicleId}, SiteId: {SiteId}, ModelId: {ModelId}",
                        vehicleId, site?.Id, model?.Id);
                    return new ComponentResponse<System.String>("Site or model not found");
                }

                _logger.LogInformation("[PERF] Loaded entities - SiteId: {SiteId}, ModelId: {ModelId}, DepartmentId: {DepartmentId}",
                    site.Id, model.Id, vehicle.DepartmentId);

                // Get driver permissions with simple queries
                var permissionsStart = Stopwatch.StartNew();
                var driverPermissions = await GetDriverPermissionsAsync();
                permissionsStart.Stop();
                _logger.LogInformation("[PERF] Driver permissions query completed in {ElapsedMs}ms, found {Count} permissions",
                    permissionsStart.ElapsedMilliseconds, driverPermissions?.Count ?? 0);

                // Handle department change cleanup if this is a department change operation
                if (vehicleAccessMessage.IsDepartmentChange && vehicleAccessMessage.OldDepartmentId.HasValue)
                {
                    var cleanupStart = Stopwatch.StartNew();
                    await ProcessDepartmentChangeCleanupAsync(vehicle, model, vehicleAccessMessage.OldDepartmentId.Value, driverPermissions);
                    cleanupStart.Stop();
                    _logger.LogInformation("[PERF] Department change cleanup completed in {ElapsedMs}ms", cleanupStart.ElapsedMilliseconds);
                }

                if (driverPermissions?.Any() != true)
                {
                    _logger.LogWarning("[PERF] No driver permissions found");
                    return new ComponentResponse<System.String>("No driver permissions found");
                }

                // Get site accesses using simplified approach
                var siteAccessStart = Stopwatch.StartNew();
                var siteAccesses = await GetSiteAccessesAsync(site.Id, driverPermissions);
                siteAccessStart.Stop();
                _logger.LogInformation("[PERF] Site accesses query completed in {ElapsedMs}ms, found {Count} accesses",
                    siteAccessStart.ElapsedMilliseconds, siteAccesses?.Count ?? 0);

                if (!siteAccesses?.Any() == true)
                {
                    _logger.LogWarning("[PERF] No site accesses found for SiteId: {SiteId}", site.Id);
                    return new ComponentResponse<System.String>("No site accesses found");
                }

                // Load cards in batch to avoid N+1 query problem
                var cardLoadStart = Stopwatch.StartNew();
                var cardLoadCount = 0;

                // Collect all CardIds that need to be loaded
                var cardIdsToLoad = siteAccesses
                    .Where(sa => sa.Card == null && sa.CardId != Guid.Empty)
                    .Select(sa => sa.CardId)
                    .Distinct()
                    .ToList();

                if (cardIdsToLoad.Any())
                {
                    _logger.LogInformation("[PERF] Loading {Count} cards in batch to avoid N+1 queries", cardIdsToLoad.Count);

                    // Single batch query to load all cards at once
                    var loadedCards = new Dictionary<Guid, CardDataObject>();

                    // Load cards in configurable batches to allow performance tuning
                    var batchSize = _configuration.GetValue<int>("VehicleAccessCreation:CardLoadBatchSize", 100); // Default: 50
                    _logger.LogDebug("[PERF] Using card loading batch size: {BatchSize}", batchSize);

                    for (int i = 0; i < cardIdsToLoad.Count; i += batchSize)
                    {
                        var batchCardIds = cardIdsToLoad.Skip(i).Take(batchSize).ToList();

                        // Use simple OR clause with small batches (much faster compilation)
                        var inClause = string.Join(" OR ", batchCardIds.Select((_, index) => $"Id == @{index}"));
                        var queryParameters = batchCardIds.Cast<object>().ToArray();

                        _logger.LogDebug("[PERF_DEBUG] Processing card batch {BatchNum}/{TotalBatches} with {Count} cards",
                            (i / batchSize) + 1, (cardIdsToLoad.Count + batchSize - 1) / batchSize, batchCardIds.Count);
                        // Test if the bottleneck is in entity access
                        var entityAccessStart = Stopwatch.StartNew();

                        var batchCards = await _dataFacade.CardDataProvider.GetCollectionAsync(
                            null, inClause, queryParameters, skipSecurity: true);

                        var cardCount = 0;
                        if (batchCards?.Any() == true)
                        {
                            foreach (var card in batchCards)
                            {
                                loadedCards[card.Id] = card;
                                cardCount++;
                            }
                        }
                        entityAccessStart.Stop();
                        _logger.LogDebug("[PERF_DEBUG] Entity access/mapping completed in {ElapsedMs}ms for {Count} cards",
                            entityAccessStart.ElapsedMilliseconds, cardCount);
                    }

                    // Map loaded cards back to their siteAccess objects
                    foreach (var siteAccess in siteAccesses)
                    {
                        if (siteAccess.Card == null && loadedCards.TryGetValue(siteAccess.CardId, out var card))
                        {
                            siteAccess.Card = card;
                            cardLoadCount++;
                        }
                    }
                }

                cardLoadStart.Stop();
                _logger.LogInformation("[PERF] Card batch loading completed in {ElapsedMs}ms, loaded {Count} cards (avoided {AvoidedQueries} individual queries)",
                    cardLoadStart.ElapsedMilliseconds, cardLoadCount, cardIdsToLoad.Count);

                // Filter out accesses without valid cards
                var validSiteAccesses = siteAccesses.Where(sa => sa.Card != null).ToList();
                _logger.LogInformation("[PERF] Valid site accesses after card filtering: {ValidCount}/{TotalCount}",
                    validSiteAccesses.Count, siteAccesses.Count);

                // Get existing accesses using simplified queries
                var existingAccessStart = Stopwatch.StartNew();
                var existingModelAccesses = await GetExistingModelAccessesSimplifiedAsync(model.Id, vehicle.DepartmentId, driverPermissions);
                var existingVehicleAccesses = await GetExistingVehicleAccessesSimplifiedAsync(vehicle.Id, driverPermissions);
                existingAccessStart.Stop();
                _logger.LogDebug("[PERF] Existing accesses query completed in {ElapsedMs}ms, found {ModelCount} model + {VehicleCount} vehicle accesses",
                    existingAccessStart.ElapsedMilliseconds, existingModelAccesses.Count, existingVehicleAccesses.Count);

                // Create access records
                var creationStart = Stopwatch.StartNew();
                var createdCount = await CreateAccessRecordsAsync(validSiteAccesses, model, vehicle, existingModelAccesses, existingVehicleAccesses);
                creationStart.Stop();
                _logger.LogInformation("[PERF] Access records creation completed in {ElapsedMs}ms, created {Count} new records",
                    creationStart.ElapsedMilliseconds, createdCount);

                // Sync driver access to the vehicle's IoT device
                if (!string.IsNullOrEmpty(vehicleAccessMessage.IoTDevice))
                {
                    var deviceSyncStart = Stopwatch.StartNew();
                    try
                    {
                        var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
                        await deviceTwinHandler.SyncDriverToVehicle(vehicleAccessMessage.IoTDevice);
                        deviceSyncStart.Stop();
                        _logger.LogDebug("[PERF] Device sync completed in {ElapsedMs}ms for device: {IoTDevice}",
                            deviceSyncStart.ElapsedMilliseconds, vehicleAccessMessage.IoTDevice);
                    }
                    catch (Exception ex)
                    {
                        deviceSyncStart.Stop();
                        _logger.LogWarning(ex, "[PERF] Device sync failed in {ElapsedMs}ms for device: {IoTDevice} - {ErrorMessage}",
                            deviceSyncStart.ElapsedMilliseconds, vehicleAccessMessage.IoTDevice, ex.Message);
                        // Log device sync error but don't fail the entire operation
                        // The access creation was successful
                    }
                }

                stopwatch.Stop();
                _logger.LogInformation("[PERF] Vehicle access {ProcessType} COMPLETED for VehicleId: {VehicleId} in {ElapsedMs}ms. Created {Count} access records.",
                    processType, vehicleId, stopwatch.ElapsedMilliseconds, createdCount);

                return new ComponentResponse<System.String>($"Vehicle access {processType} completed successfully. Created {createdCount} new access records.");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                var processType = "unknown"; // fallback in case of early exception
                try
                {
                    var vehicleAccessMessage = System.Text.Json.JsonSerializer.Deserialize<VehicleAccessCreationMessage>(Message);
                    processType = vehicleAccessMessage?.IsDepartmentChange == true ? "department change" :
                                 vehicleAccessMessage?.IsNewVehicle == true ? "new vehicle" : "vehicle access update";
                }
                catch { /* ignore deserialization errors in error handling */ }

                _logger.LogError(ex, "[PERF] Vehicle access {ProcessType} FAILED for VehicleId: {VehicleId} after {ElapsedMs}ms - {ErrorMessage}",
                    processType, vehicleId, stopwatch.ElapsedMilliseconds, ex.Message);

                // Log the error and return failure response
                return new ComponentResponse<System.String>($"Error processing vehicle access {processType}: {ex.Message}");
            }
        }

        // Cache for driver permissions to avoid repeated database calls
        private static readonly object _permissionsCacheLock = new object();
        private static List<PermissionDataObject> _cachedDriverPermissions = null;
        private static DateTime _permissionsCacheExpiry = DateTime.MinValue;
        // Cache duration is now configurable - will be set in GetDriverPermissionsAsync

        /// <summary>
        /// Get driver permissions using optimized query with caching
        /// </summary>
        private async System.Threading.Tasks.Task<List<PermissionDataObject>> GetDriverPermissionsAsync()
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Check cache first
                lock (_permissionsCacheLock)
                {
                    if (_cachedDriverPermissions != null && DateTime.UtcNow < _permissionsCacheExpiry)
                    {
                        stopwatch.Stop();
                        _logger.LogDebug("[PERF] GetDriverPermissionsAsync returned from cache in {ElapsedMs}ms, found {Count} permissions",
                            stopwatch.ElapsedMilliseconds, _cachedDriverPermissions.Count);
                        return new List<PermissionDataObject>(_cachedDriverPermissions); // Return a copy to avoid modification
                    }
                }

                // Cache miss - fetch from database with optimized single query
                var queryStart = Stopwatch.StartNew();

                // Single query to get both level 1 and level 3 permissions
                var allPermissions = await _dataFacade.PermissionDataProvider.GetCollectionAsync(
                    null,
                    "LevelName == @0 OR LevelName == @1",
                    new object[] { 1, 3 },
                    skipSecurity: true);

                queryStart.Stop();

                var permissions = allPermissions?.ToList() ?? new List<PermissionDataObject>();

                // Update cache
                lock (_permissionsCacheLock)
                {
                    _cachedDriverPermissions = new List<PermissionDataObject>(permissions);
                    var cacheDurationMinutes = _configuration.GetValue<int>("VehicleAccessCreation:PermissionsCacheDurationMinutes", 15);
                    _permissionsCacheExpiry = DateTime.UtcNow.AddMinutes(cacheDurationMinutes);
                }

                stopwatch.Stop();
                _logger.LogDebug("[PERF] GetDriverPermissionsAsync completed in {ElapsedMs}ms (Query: {QueryMs}ms), found {Count} permissions (Level1+3), cached until {CacheExpiry}",
                    stopwatch.ElapsedMilliseconds, queryStart.ElapsedMilliseconds, permissions.Count, _permissionsCacheExpiry);

                return permissions;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] GetDriverPermissionsAsync failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
                throw;
            }
        }

        /// <summary>
        /// Clear the permissions cache (useful for testing or when permissions are updated)
        /// </summary>
        public static void ClearPermissionsCache()
        {
            lock (_permissionsCacheLock)
            {
                _cachedDriverPermissions = null;
                _permissionsCacheExpiry = DateTime.MinValue;
            }
        }

        /// <summary>
        /// Get site accesses using simplified approach
        /// </summary>
        private async System.Threading.Tasks.Task<List<SiteVehicleNormalCardAccessDataObject>> GetSiteAccessesAsync(Guid siteId, List<PermissionDataObject> permissions)
        {
            var stopwatch = Stopwatch.StartNew();
            var allSiteAccesses = new List<SiteVehicleNormalCardAccessDataObject>();

            try
            {
                foreach (var permission in permissions)
                {
                    var queryStart = Stopwatch.StartNew();
                    var siteAccesses = await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                        null, "SiteId == @0 and PermissionId == @1", new object[] { siteId, permission.Id }, skipSecurity: true);
                    queryStart.Stop();

                    if (siteAccesses?.Any() == true)
                    {
                        allSiteAccesses.AddRange(siteAccesses);
                        _logger.LogDebug("[PERF] Site access query for PermissionId {PermissionId} completed in {ElapsedMs}ms, found {Count} accesses",
                            permission.Id, queryStart.ElapsedMilliseconds, siteAccesses.Count);
                    }
                }

                stopwatch.Stop();
                _logger.LogDebug("[PERF] GetSiteAccessesAsync completed in {ElapsedMs}ms, total {Count} accesses across {PermissionCount} permissions",
                    stopwatch.ElapsedMilliseconds, allSiteAccesses.Count, permissions.Count);

                return allSiteAccesses;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] GetSiteAccessesAsync failed after {ElapsedMs}ms for SiteId: {SiteId}",
                    stopwatch.ElapsedMilliseconds, siteId);
                throw;
            }
        }

        /// <summary>
        /// Create access records with parallel batch processing and session locking
        /// </summary>
        private async System.Threading.Tasks.Task<int> CreateAccessRecordsAsync(
            List<SiteVehicleNormalCardAccessDataObject> siteAccesses,
            ModelDataObject model,
            VehicleDataObject vehicle,
            HashSet<string> existingModelKeys,
            HashSet<string> existingVehicleKeys)
        {
            var stopwatch = Stopwatch.StartNew();
            int createdCount = 0;
            int modelAccessCreated = 0;
            int vehicleAccessCreated = 0;

            try
            {
                // Prepare entities to create (without saving)
                var preparationStart = Stopwatch.StartNew();
                var modelAccessesToCreate = new List<(Guid permissionId, Guid cardId)>();
                var vehicleAccessesToCreate = new List<(Guid permissionId, Guid cardId)>();

                foreach (var siteAccess in siteAccesses)
                {
                    var cardId = siteAccess.Card.Id;
                    var permissionId = siteAccess.PermissionId;

                    // Check model access
                    var modelAccessKey = $"{model.Id}_{permissionId}_{cardId}_{vehicle.DepartmentId}";
                    if (!existingModelKeys.Contains(modelAccessKey))
                    {
                        modelAccessesToCreate.Add((permissionId, cardId));
                        modelAccessCreated++;
                    }

                    // Check vehicle access
                    var vehicleAccessKey = $"{vehicle.Id}_{permissionId}_{cardId}";
                    if (!existingVehicleKeys.Contains(vehicleAccessKey))
                    {
                        vehicleAccessesToCreate.Add((permissionId, cardId));
                        vehicleAccessCreated++;
                    }
                }
                preparationStart.Stop();
                _logger.LogDebug("[PERF] Entity preparation completed in {ElapsedMs}ms, prepared {ModelCount} model + {VehicleCount} vehicle accesses",
                    preparationStart.ElapsedMilliseconds, modelAccessCreated, vehicleAccessCreated);

                // IMPORTANT: Reduced parallelism to prevent race conditions on the same vehicle
                // Process in smaller batches with limited concurrency - now configurable for performance tuning
                var modelAccessBatchSize = _configuration.GetValue<int>("VehicleAccessCreation:ModelAccessBatchSize", 100);
                var vehicleAccessBatchSize = _configuration.GetValue<int>("VehicleAccessCreation:VehicleAccessBatchSize", 100);
                var maxConcurrency = _configuration.GetValue<int>("VehicleAccessCreation:MaxConcurrency", 8);

                _logger.LogDebug("[PERF] Using access creation settings - ModelBatch: {ModelBatch}, VehicleBatch: {VehicleBatch}, MaxConcurrency: {MaxConcurrency}",
                    modelAccessBatchSize, vehicleAccessBatchSize, maxConcurrency);

                // Process model accesses first (less likely to have conflicts)
                if (modelAccessesToCreate.Any())
                {
                    var modelSaveStart = Stopwatch.StartNew();
                    var modelBatches = CreateBatches(modelAccessesToCreate, modelAccessBatchSize);

                    var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
                    var modelTasks = modelBatches.Select(async batch =>
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            return await SaveModelAccessBatchAsync(batch, model.Id, vehicle.DepartmentId);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    });

                    var modelResults = await System.Threading.Tasks.Task.WhenAll(modelTasks);
                    var modelSavedCount = modelResults.AsEnumerable().Sum();
                    modelSaveStart.Stop();

                    _logger.LogDebug("[PERF] Model accesses parallel save completed in {ElapsedMs}ms for {Count} records in {BatchCount} batches",
                        modelSaveStart.ElapsedMilliseconds, modelSavedCount, modelBatches.Count);
                }

                // Process vehicle accesses sequentially if department change cleanup is in progress
                // This prevents race conditions during department changes
                var isCleanupInProgress = await IsDepartmentChangeCleanupInProgressAsync(vehicle.Id);

                if (vehicleAccessesToCreate.Any())
                {
                    if (isCleanupInProgress)
                    {
                        _logger.LogWarning("[PERF] Department change cleanup detected for vehicle {VehicleId}. Processing vehicle accesses sequentially to prevent race conditions.", vehicle.Id);

                        // Process sequentially to avoid race conditions
                        var sequentialStart = Stopwatch.StartNew();
                        var savedCount = await SaveVehicleAccessSequentiallyAsync(vehicleAccessesToCreate, vehicle.Id);
                        sequentialStart.Stop();

                        _logger.LogDebug("[PERF] Vehicle accesses sequential save completed in {ElapsedMs}ms for {Count} records",
                            sequentialStart.ElapsedMilliseconds, savedCount);
                    }
                    else
                    {
                        // Process in parallel batches with reduced concurrency
                        var vehicleSaveStart = Stopwatch.StartNew();
                        var vehicleBatches = CreateBatches(vehicleAccessesToCreate, vehicleAccessBatchSize);

                        var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
                        var vehicleTasks = vehicleBatches.Select(async batch =>
                        {
                            await semaphore.WaitAsync();
                            try
                            {
                                return await SaveVehicleAccessBatchAsync(batch, vehicle.Id);
                            }
                            finally
                            {
                                semaphore.Release();
                            }
                        });

                        var vehicleResults = await System.Threading.Tasks.Task.WhenAll(vehicleTasks);
                        var vehicleSavedCount = vehicleResults.AsEnumerable().Sum();
                        vehicleSaveStart.Stop();

                        _logger.LogDebug("[PERF] Vehicle accesses parallel save completed in {ElapsedMs}ms for {Count} records in {BatchCount} batches",
                            vehicleSaveStart.ElapsedMilliseconds, vehicleSavedCount, vehicleBatches.Count);
                    }
                }

                createdCount = modelAccessCreated + vehicleAccessCreated;

                stopwatch.Stop();
                _logger.LogInformation("[PERF] CreateAccessRecordsAsync completed in {ElapsedMs}ms, created {ModelCount} model + {VehicleCount} vehicle accesses (total: {TotalCount})",
                    stopwatch.ElapsedMilliseconds, modelAccessCreated, vehicleAccessCreated, createdCount);

                return createdCount;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] CreateAccessRecordsAsync failed after {ElapsedMs}ms, attempted {Count} records",
                    stopwatch.ElapsedMilliseconds, createdCount);
                throw;
            }
        }

        /// <summary>
        /// Split items into batches of specified size
        /// </summary>
        private List<List<T>> CreateBatches<T>(List<T> items, int batchSize)
        {
            var batches = new List<List<T>>();
            for (int i = 0; i < items.Count; i += batchSize)
            {
                batches.Add(items.Skip(i).Take(batchSize).ToList());
            }
            return batches;
        }

        /// <summary>
        /// Save a batch of model accesses in a separate transaction
        /// </summary>
        private async System.Threading.Tasks.Task<int> SaveModelAccessBatchAsync(List<(Guid permissionId, Guid cardId)> batch, Guid modelId, Guid departmentId)
        {
            try
            {
                var batchStart = Stopwatch.StartNew();
                int savedCount = 0;

                // Create isolated scope for this batch to avoid NHibernate session conflicts
                using var scope = _serviceScopeFactory.CreateScope();
                var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();

                foreach (var (permissionId, cardId) in batch)
                {
                    var modelAccess = scope.ServiceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    modelAccess.PermissionId = permissionId;
                    modelAccess.ModelId = modelId;
                    modelAccess.CardId = cardId;
                    modelAccess.DepartmentId = departmentId;

                    await scopedDataFacade.ModelVehicleNormalCardAccessDataProvider.SaveAsync(modelAccess, skipSecurity: true);
                    savedCount++;
                }

                batchStart.Stop();
                if (batchStart.ElapsedMilliseconds > 5000) // Log slow batches
                {
                    _logger.LogWarning("[PERF] Slow model access batch: {ElapsedMs}ms for {Count} records",
                        batchStart.ElapsedMilliseconds, batch.Count);
                }

                return savedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Model access batch save failed for {Count} records", batch.Count);
                throw;
            }
        }

        /// <summary>
        /// Save a batch of vehicle accesses in a separate transaction with race condition protection
        /// </summary>
        private async System.Threading.Tasks.Task<int> SaveVehicleAccessBatchAsync(List<(Guid permissionId, Guid cardId)> batch, Guid vehicleId)
        {
            try
            {
                var batchStart = Stopwatch.StartNew();
                int savedCount = 0;

                // Create isolated scope for this batch to avoid NHibernate session conflicts
                using var scope = _serviceScopeFactory.CreateScope();
                var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();

                foreach (var (permissionId, cardId) in batch)
                {
                    try
                    {
                        // IMPORTANT: Check for existing record before creating to prevent race conditions
                        var existingAccess = await scopedDataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                            null,
                            "VehicleId == @0 AND CardId == @1 AND PermissionId == @2",
                            new object[] { vehicleId, cardId, permissionId },
                            skipSecurity: true);

                        if (existingAccess?.Any() == true)
                        {
                            _logger.LogDebug("PerVehicleNormalCardAccess already exists for Vehicle: {VehicleId}, Card: {CardId}, Permission: {PermissionId}. Skipping creation.",
                                vehicleId, cardId, permissionId);
                            continue;
                        }

                        var vehicleAccess = scope.ServiceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        vehicleAccess.PermissionId = permissionId;
                        vehicleAccess.VehicleId = vehicleId;
                        vehicleAccess.CardId = cardId;

                        await scopedDataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleAccess, skipSecurity: true);
                        savedCount++;
                    }
                    catch (Exception ex)
                    {
                        // Handle race condition scenarios gracefully
                        if (IsRaceConditionOrDuplicateError(ex))
                        {
                            _logger.LogWarning("Race condition or duplicate detected while saving PerVehicleNormalCardAccess for Vehicle: {VehicleId}, Card: {CardId}, Permission: {PermissionId}. Likely created by concurrent process: {Message}",
                                vehicleId, cardId, permissionId, ex.Message);

                            // Continue processing other records
                            continue;
                        }

                        _logger.LogError(ex, "Failed to save PerVehicleNormalCardAccess for Vehicle: {VehicleId}, Card: {CardId}, Permission: {PermissionId}: {Message}",
                            vehicleId, cardId, permissionId, ex.Message);

                        // Continue with other records rather than failing the entire batch
                        continue;
                    }
                }

                batchStart.Stop();
                if (batchStart.ElapsedMilliseconds > 5000) // Log slow batches
                {
                    _logger.LogWarning("[PERF] Slow vehicle access batch: {ElapsedMs}ms for {Count} records (saved: {SavedCount})",
                        batchStart.ElapsedMilliseconds, batch.Count, savedCount);
                }

                return savedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Vehicle access batch save failed for {Count} records", batch.Count);
                throw;
            }
        }

        /// <summary>
        /// Determines if an exception is likely due to a race condition or duplicate key constraint
        /// </summary>
        private bool IsRaceConditionOrDuplicateError(Exception ex)
        {
            var message = ex.Message?.ToLowerInvariant() ?? "";
            var innerMessage = ex.InnerException?.Message?.ToLowerInvariant() ?? "";

            return message.Contains("duplicate") ||
                   message.Contains("unique constraint") ||
                   message.Contains("primary key") ||
                   message.Contains("violation") ||
                   message.Contains("conflict") ||
                   message.Contains("batch update returned unexpected row count") ||
                   message.Contains("actual row count: 0; expected: 1") ||
                   message.Contains("could not execute batch command") ||
                   message.Contains("row was updated or deleted by another transaction") ||
                   message.Contains("optimistic locking") ||
                   message.Contains("concurrency") ||
                   innerMessage.Contains("duplicate") ||
                   innerMessage.Contains("unique constraint") ||
                   innerMessage.Contains("primary key") ||
                   innerMessage.Contains("violation") ||
                   innerMessage.Contains("conflict") ||
                   innerMessage.Contains("row count");
        }

        /// <summary>
        /// Get existing model accesses using simplified approach
        /// </summary>
        private async System.Threading.Tasks.Task<HashSet<string>> GetExistingModelAccessesSimplifiedAsync(
            Guid modelId, Guid departmentId, List<PermissionDataObject> permissions)
        {
            var stopwatch = Stopwatch.StartNew();
            var existingKeys = new HashSet<string>();

            try
            {
                foreach (var permission in permissions)
                {
                    var queryStart = Stopwatch.StartNew();
                    var existing = await _dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                        null, "ModelId == @0 and DepartmentId == @1 and PermissionId == @2",
                        new object[] { modelId, departmentId, permission.Id }, skipSecurity: true);
                    queryStart.Stop();

                    if (existing?.Any() == true)
                    {
                        foreach (var item in existing)
                        {
                            existingKeys.Add($"{item.ModelId}_{item.PermissionId}_{item.CardId}_{item.DepartmentId}");
                        }

                        _logger.LogDebug("[PERF] Model access query for PermissionId {PermissionId} completed in {ElapsedMs}ms, found {Count} existing",
                            permission.Id, queryStart.ElapsedMilliseconds, existing.Count);
                    }
                }

                stopwatch.Stop();
                _logger.LogDebug("[PERF] GetExistingModelAccessesSimplifiedAsync completed in {ElapsedMs}ms, found {Count} existing accesses",
                    stopwatch.ElapsedMilliseconds, existingKeys.Count);

                return existingKeys;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] GetExistingModelAccessesSimplifiedAsync failed after {ElapsedMs}ms for ModelId: {ModelId}",
                    stopwatch.ElapsedMilliseconds, modelId);
                throw;
            }
        }

        /// <summary>
        /// Get existing vehicle accesses using simplified approach
        /// </summary>
        private async System.Threading.Tasks.Task<HashSet<string>> GetExistingVehicleAccessesSimplifiedAsync(
            Guid vehicleId, List<PermissionDataObject> permissions)
        {
            var stopwatch = Stopwatch.StartNew();
            var existingKeys = new HashSet<string>();

            try
            {
                foreach (var permission in permissions)
                {
                    var queryStart = Stopwatch.StartNew();
                    var existing = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                        null, "VehicleId == @0 and PermissionId == @1",
                        new object[] { vehicleId, permission.Id }, skipSecurity: true);
                    queryStart.Stop();

                    if (existing?.Any() == true)
                    {
                        foreach (var item in existing)
                        {
                            existingKeys.Add($"{item.VehicleId}_{item.PermissionId}_{item.CardId}");
                        }

                        _logger.LogDebug("[PERF] Vehicle access query for PermissionId {PermissionId} completed in {ElapsedMs}ms, found {Count} existing",
                            permission.Id, queryStart.ElapsedMilliseconds, existing.Count);
                    }
                }

                stopwatch.Stop();
                _logger.LogDebug("[PERF] GetExistingVehicleAccessesSimplifiedAsync completed in {ElapsedMs}ms, found {Count} existing accesses",
                    stopwatch.ElapsedMilliseconds, existingKeys.Count);

                return existingKeys;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] GetExistingVehicleAccessesSimplifiedAsync failed after {ElapsedMs}ms for VehicleId: {VehicleId}",
                    stopwatch.ElapsedMilliseconds, vehicleId);
                throw;
            }
        }

        /// <summary>
        /// Process department change cleanup - removes old access records and handles model cleanup
        /// </summary>
        private async System.Threading.Tasks.Task ProcessDepartmentChangeCleanupAsync(
            VehicleDataObject vehicle,
            ModelDataObject model,
            Guid oldDepartmentId,
            List<PermissionDataObject> driverPermissions)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("[PERF] Starting department change cleanup for VehicleId: {VehicleId}, OldDepartmentId: {OldDepartmentId}, NewDepartmentId: {NewDepartmentId}",
                    vehicle.Id, oldDepartmentId, vehicle.DepartmentId);

                // 1. First load and mark for deletion all existing vehicle access
                var vehicleAccessCleanupStart = Stopwatch.StartNew();
                await vehicle.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
                if (vehicle.PerVehicleNormalCardAccessItems?.Any() == true)
                {
                    foreach (var access in vehicle.PerVehicleNormalCardAccessItems)
                    {
                        access.IsMarkedForDeletion = true;
                    }

                    // save vehicle
                    await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    _logger.LogDebug("[PERF] Marked {Count} vehicle access records for deletion", vehicle.PerVehicleNormalCardAccessItems.Count);
                }
                vehicleAccessCleanupStart.Stop();
                _logger.LogDebug("[PERF] Vehicle access cleanup completed in {ElapsedMs}ms", vehicleAccessCleanupStart.ElapsedMilliseconds);

                // 2. Check if there are other vehicles of the same model in the old department
                var modelCleanupStart = Stopwatch.StartNew();
                var otherVehiclesWithSameModelInOldDept = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                    null,
                    "ModelId == @0 AND DepartmentId == @1",
                    new object[] { model.Id, oldDepartmentId },
                    skipSecurity: true);

                // If no other vehicles of this model in the old department, clean up model access
                if (!otherVehiclesWithSameModelInOldDept.Any())
                {
                    await model.LoadModelVehicleNormalCardAccessItemsAsync(skipSecurity: true);
                    if (model.ModelVehicleNormalCardAccessItems?.Any() == true)
                    {
                        var modelAccessesToDelete = model.ModelVehicleNormalCardAccessItems.Where(a => a.DepartmentId == oldDepartmentId).ToList();
                        foreach (var modelAccess in modelAccessesToDelete)
                        {
                            modelAccess.IsMarkedForDeletion = true;
                        }

                        if (modelAccessesToDelete.Any())
                        {
                            // save model
                            await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
                            _logger.LogDebug("[PERF] Marked {Count} model access records for deletion", modelAccessesToDelete.Count);
                        }
                    }
                }
                else
                {
                    _logger.LogDebug("[PERF] Found {Count} other vehicles with same model in old department, skipping model access cleanup",
                        otherVehiclesWithSameModelInOldDept.Count);
                }
                modelCleanupStart.Stop();
                _logger.LogDebug("[PERF] Model access cleanup check completed in {ElapsedMs}ms", modelCleanupStart.ElapsedMilliseconds);

                stopwatch.Stop();
                _logger.LogInformation("[PERF] Department change cleanup COMPLETED for VehicleId: {VehicleId} in {ElapsedMs}ms",
                    vehicle.Id, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] Department change cleanup FAILED for VehicleId: {VehicleId} after {ElapsedMs}ms - {ErrorMessage}",
                    vehicle.Id, stopwatch.ElapsedMilliseconds, ex.Message);
                throw;
            }
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> ManageUserAccessAsync(string Message, Dictionary<string, object> parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();
            var personId = Guid.Empty;

            try
            {
                _logger.LogInformation("[PERF] Starting user access update process");

                // Parse the JSON message to get user access update information
                var parseStart = Stopwatch.StartNew();
                var userAccessMessage = System.Text.Json.JsonSerializer.Deserialize<UserAccessUpdateMessage>(Message);
                parseStart.Stop();
                _logger.LogDebug("[PERF] Message parsing completed in {ElapsedMs}ms", parseStart.ElapsedMilliseconds);

                if (userAccessMessage == null)
                {
                    _logger.LogWarning("[PERF] Invalid user access message format received");
                    return new ComponentResponse<string>("Invalid message format");
                }

                personId = userAccessMessage.PersonId;
                _logger.LogInformation("[PERF] Processing user access update for PersonId: {PersonId}, CorrelationId: {CorrelationId}",
                    personId, userAccessMessage.CorrelationId);

                // Get the VehicleAccessUtilities component to process the access updates
                var vehicleAccessUtilities = _serviceProvider.GetRequiredService<IVehicleAccessUtilities>();

                // Deserialize the access collections from JSON
                var deserializeStart = Stopwatch.StartNew();
                var personToSiteAccesses = DeserializeAccessCollection<PersonToSiteVehicleNormalAccessViewDataObject>(
                    userAccessMessage.PersonToSiteAccessesJson, "PersonToSiteVehicleNormalAccessView");
                var personToDepartmentAccesses = DeserializeAccessCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>(
                    userAccessMessage.PersonToDepartmentAccessesJson, "PersonToDepartmentVehicleNormalAccessView");
                var personToModelAccesses = DeserializeAccessCollection<PersonToModelVehicleNormalAccessViewDataObject>(
                    userAccessMessage.PersonToModelAccessesJson, "PersonToModelVehicleNormalAccessView");
                // Vehicle accesses are compressed, so use decompression method
                var personToVehicleAccesses = DecompressAccessCollection<PersonToPerVehicleNormalAccessViewDataObject>(
                    userAccessMessage.PersonToVehicleAccessesJson, "PersonToPerVehicleNormalAccessView");
                deserializeStart.Stop();
                _logger.LogDebug("[PERF] Access collections deserialization completed in {ElapsedMs}ms", deserializeStart.ElapsedMilliseconds);

                // Call the internal processing method directly (bypassing the API layer)
                var processingStart = Stopwatch.StartNew();
                var utilitiesImpl = (VehicleAccessUtilities)vehicleAccessUtilities.ComponentClass;
                var result = await utilitiesImpl.UpdateAccessesForPersonInternalAsync(
                    personToSiteAccesses,
                    personToDepartmentAccesses,
                    personToModelAccesses,
                    personToVehicleAccesses,
                    personId,
                    parameters);
                processingStart.Stop();
                _logger.LogInformation("[PERF] User access processing completed in {ElapsedMs}ms for PersonId: {PersonId}",
                    processingStart.ElapsedMilliseconds, personId);

                if (result.Result)
                {
                    stopwatch.Stop();
                    _logger.LogInformation("[PERF] Successfully processed user access update for PersonId: {PersonId} in {TotalMs}ms",
                        personId, stopwatch.ElapsedMilliseconds);
                    return new ComponentResponse<string>($"Successfully updated access for person {personId}");
                }
                else
                {
                    stopwatch.Stop();
                    _logger.LogWarning("[PERF] User access update failed for PersonId: {PersonId} after {TotalMs}ms",
                        personId, stopwatch.ElapsedMilliseconds);
                    return new ComponentResponse<string>($"Failed to update access for person {personId}");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "[PERF] Error processing user access update for PersonId: {PersonId} after {TotalMs}ms: {Message}",
                    personId, stopwatch.ElapsedMilliseconds, ex.Message);
                return new ComponentResponse<string>($"Error processing user access update: {ex.Message}");
            }
        }

        /// <summary>
        /// Deserializes access collection from JSON string
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="json">JSON string containing the access collection</param>
        /// <param name="containerTypeName">Name of the container type for logging</param>
        /// <returns>DataObjectCollection of the specified type</returns>
        private DataObjectCollection<T> DeserializeAccessCollection<T>(string json, string containerTypeName) where T : class, IDataObject
        {
            var collection = new DataObjectCollection<T>();
            // Initialize the ObjectsDataSet property to prevent null reference exceptions
            collection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            if (string.IsNullOrEmpty(json))
            {
                _logger.LogDebug("[PERF] Empty JSON for {ContainerType}, returning empty collection", containerTypeName);
                return collection;
            }

            try
            {
                // Deserialize to dynamic objects first, then create data objects using DI
                var dynamicItems = JsonConvert.DeserializeObject<List<dynamic>>(json);

                if (dynamicItems != null)
                {
                    foreach (var dynamicItem in dynamicItems)
                    {
                        // Create the data object using dependency injection
                        var item = _serviceProvider.GetRequiredService<T>();

                        // Use reflection to set properties from the dynamic object
                        var itemType = typeof(T);
                        foreach (var property in itemType.GetProperties())
                        {
                            if (property.CanWrite)
                            {
                                try
                                {
                                    var dynamicDict = (Newtonsoft.Json.Linq.JObject)dynamicItem;
                                    if (dynamicDict.TryGetValue(property.Name, out var value))
                                    {
                                        var convertedValue = value.ToObject(property.PropertyType);
                                        property.SetValue(item, convertedValue);
                                    }
                                }
                                catch (Exception propEx)
                                {
                                    _logger.LogDebug("Could not set property {PropertyName}: {Error}", property.Name, propEx.Message);
                                    // Continue with other properties
                                }
                            }
                        }

                        collection.Add(item);
                    }
                    _logger.LogDebug("[PERF] Deserialized {Count} items for {ContainerType}", dynamicItems.Count, containerTypeName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Failed to deserialize {ContainerType} from JSON", containerTypeName);
                // Return empty collection rather than throwing, to allow partial processing
            }

            return collection;
        }

        /// <summary>
        /// Decompresses and deserializes access collection from Base64-encoded GZip string
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="compressedData">Base64-encoded compressed JSON string</param>
        /// <param name="containerTypeName">Name of the container type for logging</param>
        /// <returns>DataObjectCollection of the specified type</returns>
        private DataObjectCollection<T> DecompressAccessCollection<T>(string compressedData, string containerTypeName) where T : class, IDataObject
        {
            var collection = new DataObjectCollection<T>();
            // Initialize the ObjectsDataSet property to prevent null reference exceptions
            collection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            if (string.IsNullOrEmpty(compressedData))
            {
                _logger.LogDebug("[PERF] Empty compressed data for {ContainerType}, returning empty collection", containerTypeName);
                return collection;
            }

            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Decode from Base64 and decompress
                var compressedBytes = Convert.FromBase64String(compressedData);
                string json;

                using (var input = new MemoryStream(compressedBytes))
                using (var gzip = new GZipStream(input, CompressionMode.Decompress))
                using (var reader = new StreamReader(gzip, Encoding.UTF8))
                {
                    json = reader.ReadToEnd();
                }

                var decompressedSize = Encoding.UTF8.GetByteCount(json);
                var compressionRatio = (double)decompressedSize / compressedBytes.Length;

                stopwatch.Stop();
                _logger.LogInformation("[PERF] Decompressed {ContainerType}: {CompressedSize} bytes -> {DecompressedSize} bytes (ratio: {Ratio:F2}x) in {ElapsedMs}ms",
                    containerTypeName, compressedBytes.Length, decompressedSize, compressionRatio, stopwatch.ElapsedMilliseconds);

                // Now deserialize the JSON using the existing method
                return DeserializeAccessCollection<T>(json, containerTypeName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Failed to decompress {ContainerType}, attempting regular deserialization", containerTypeName);
                // Fallback to regular deserialization in case the data wasn't compressed
                return DeserializeAccessCollection<T>(compressedData, containerTypeName);
            }
        }

        /// <summary>
        /// Check if department change cleanup is in progress for a vehicle
        /// </summary>
        private async System.Threading.Tasks.Task<bool> IsDepartmentChangeCleanupInProgressAsync(Guid vehicleId)
        {
            try
            {
                // Check if there are any PerVehicleNormalCardAccess records marked for deletion
                // This indicates cleanup is in progress
                var existingAccesses = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                    null,
                    "VehicleId == @0",
                    new object[] { vehicleId },
                    skipSecurity: true);

                return existingAccesses?.Any(a => a.IsMarkedForDeletion) == true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to check cleanup status for vehicle {VehicleId}. Assuming cleanup in progress for safety.", vehicleId);
                return true; // Err on the side of caution
            }
        }

        /// <summary>
        /// Save vehicle accesses sequentially to avoid race conditions
        /// </summary>
        private async System.Threading.Tasks.Task<int> SaveVehicleAccessSequentiallyAsync(List<(Guid permissionId, Guid cardId)> vehicleAccesses, Guid vehicleId)
        {
            int savedCount = 0;

            // Use a single scoped data facade for all operations to maintain consistency
            using var scope = _serviceScopeFactory.CreateScope();
            var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();

            foreach (var (permissionId, cardId) in vehicleAccesses)
            {
                try
                {
                    // Check if record still doesn't exist (avoid duplicates)
                    var existing = await scopedDataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                        null,
                        "VehicleId == @0 AND CardId == @1 AND PermissionId == @2",
                        new object[] { vehicleId, cardId, permissionId },
                        skipSecurity: true);

                    if (existing?.Any() != true)
                    {
                        var vehicleAccess = scope.ServiceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        vehicleAccess.PermissionId = permissionId;
                        vehicleAccess.VehicleId = vehicleId;
                        vehicleAccess.CardId = cardId;

                        await scopedDataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleAccess, skipSecurity: true);
                        savedCount++;
                    }
                    else
                    {
                        _logger.LogDebug("PerVehicleNormalCardAccess already exists for Vehicle: {VehicleId}, Card: {CardId}, Permission: {PermissionId}",
                            vehicleId, cardId, permissionId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to save PerVehicleNormalCardAccess for Vehicle: {VehicleId}, Card: {CardId}, Permission: {PermissionId}",
                        vehicleId, cardId, permissionId);

                    // Continue with other records rather than failing completely
                    continue;
                }
            }

            return savedCount;
        }

    }
}
