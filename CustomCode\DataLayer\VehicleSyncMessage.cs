using System;
using System.Collections.Generic;

namespace FleetXQ.Data.DataObjects
{
    /// <summary>
    /// Message for queuing individual vehicle sync operations to avoid blocking user access updates
    /// Each message handles one vehicle for better parallelization and error isolation
    /// </summary>
    public class VehicleSyncMessage
    {
        public Guid VehicleId { get; set; }
        public Guid? InitiatedByUserId { get; set; }
        public Guid PersonId { get; set; }
        public Guid CustomerId { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CorrelationId { get; set; }
        public string Priority { get; set; } = "Normal";
        public string SyncReason { get; set; } // e.g., "UserAccessUpdate", "VehicleCreation", "Manual"
        public int VehicleSequence { get; set; } // Which vehicle in the batch (1, 2, 3...)
        public int TotalVehicles { get; set; } // Total vehicles in this sync operation
    }
}