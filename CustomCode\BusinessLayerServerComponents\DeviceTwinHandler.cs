using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public partial class DeviceTwinHandler : IDeviceTwinHandler
    {

        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILoggingService _logger;
        private readonly IConfiguration _configuration;
        private readonly IStorageClientFactory _storageClientFactory;
        private readonly IVehicleSyncQueueService _vehicleSyncQueueService;

        public DeviceTwinHandler(IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, IServiceProvider serviceProvider, ILoggingService logger, IConfiguration configuration, IStorageClientFactory storageClientFactory, IVehicleSyncQueueService vehicleSyncQueueService)
        {
            _deviceMessageHandler = deviceMessageHandler;
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _logger = logger;
            _configuration = configuration;
            _storageClientFactory = storageClientFactory;
            _vehicleSyncQueueService = vehicleSyncQueueService;
        }

        // Helper method to map ChecklistLanguageEnum to language codes
        private string GetLanguageCode(ChecklistLanguageEnum? language)
        {
            return language switch
            {
                ChecklistLanguageEnum.English => "en",
                ChecklistLanguageEnum.Thai => "th",
                ChecklistLanguageEnum.Spanish => "es",
                ChecklistLanguageEnum.Vietnamese => "vi",
                ChecklistLanguageEnum.French => "fr",
                ChecklistLanguageEnum.Filipino => "tl",
                ChecklistLanguageEnum.TraditionalChinese => "zh",
                _ => "en" // Default to English
            };
        }

        // this syncs OndeDemandSetting and Ondemand Users to the vehicle
        public async Task SyncOnDemandSettingAndUsersAsync(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var desired = new
            {
                on_demand = await PrepareOnDemandSettingAsync(data.Vehicle),
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 1); // syncType = 1
        }

        //This will update the device twin vehicle access list
        public async Task SyncDriverToVehicle(string deviceId, Guid? userId = null)
        {
            _logger?.LogInformation($"[PERF] SyncDriverToVehicle called with {deviceId}");
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            // Use queue-based approach instead of direct sync (matches pattern from VehicleAccessUtilities)
            var correlationId = Guid.NewGuid().ToString();
            var syncMessage = new VehicleSyncMessage
            {
                VehicleId = data.Vehicle.Id,
                PersonId = userId ?? Guid.NewGuid(), // Use userId or default for DeviceTwin operations
                CustomerId = data.Vehicle.CustomerId,
                InitiatedByUserId = userId,
                CreatedAt = DateTime.UtcNow,
                CorrelationId = correlationId,
                Priority = "Normal",
                SyncReason = "DriverListSync",
                VehicleSequence = 1,
                TotalVehicles = 1
            };

            await _vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
            _logger?.LogInformation($"[PERF] Vehicle sync message queued for device {deviceId}, vehicle {data.Vehicle.Id} with correlation {correlationId}");
        }

        public async Task SyncChecklistToVehicle(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            object desiredProperties = new
            {
                check_list = await PrepareChecklistAsync(data.Vehicle),
                checklist_time = await PrepareChecklistSettingAsync(data.Vehicle),
                survey_timeout_setting = await PrepareSurveyTimeoutAsync(data.Vehicle),
                comment = await PrepareShowCommentAsync(data.Vehicle),
                preop_randomization = await PrepareRandomizeAsync(data.Vehicle),
                multilang_enabled = await PrepareIsMultilangEnabledAsync(data.Vehicle)
            };

            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desiredProperties, 2); // syncType = 2
        }

        private int? FormatTime(short? hour, short? minute)
        {
            var hourValue = 0;
            var minuteValue = 0;

            if (hour == null && minute == null)
            {
                return null;
            }

            if (hour != null)
            {
                hourValue = hour.Value;
            }

            if (minute != null)
            {
                minuteValue = minute.Value;
            }

            int formattedTime = hourValue * 100 + minuteValue;

            return formattedTime;
        }

        public async Task SyncChecklistSettingToVehicle(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var desired = new
            {
                checklist_time = await PrepareChecklistSettingAsync(data.Vehicle),
                survey_timeout_setting = await PrepareSurveyTimeoutAsync(data.Vehicle),
                comment = await PrepareShowCommentAsync(data.Vehicle),
                preop_randomization = await PrepareRandomizeAsync(data.Vehicle),
                multilang_enabled = await PrepareIsMultilangEnabledAsync(data.Vehicle)
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 3); // syncType = 3
        }

        public async Task SyncGeneralSettings(string deviceId)
        {
            DesiredMessage desiredMessage = new();
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var module = data.Module;
            var vehicle = data.Vehicle;
            var vehicleDiagnostic = await vehicle.LoadVehicleDiagnosticAsync();
            var departmentChecklist = await vehicle.LoadDepartmentChecklistAsync(skipSecurity: true);

            //###################### OTHER SETTINGS  #######################
            var idleTime = vehicle.IDLETimer;
            if (!vehicle.TimeoutEnabled)
            {
                idleTime = 0;
            }
            var idleSetting = new
            {
                idle_time = idleTime,
                idle_mode = 0,
                idle_input = 16
            };

            float multi = (float)(module.FSSXMulti == 0 ? 1 : ((module.FSSXMulti / 100) + 1));
            var redImpactThreshold = (int)(module.FSSSBase * multi * 10);

            if (!vehicle.ImpactLockout)
            {
                redImpactThreshold = 0;
            }
            else if (vehicleDiagnostic != null && vehicleDiagnostic.DatabaseRedImpactThreshold > 0)
            {
                redImpactThreshold = (int)vehicleDiagnostic.DatabaseRedImpactThreshold;
            }

            var impactLockout = new
            {
                red_impact_threshold = redImpactThreshold
            };

            var reportIntervalSec = desiredMessage.ReportIntervalSec;

            var CanruleId = vehicle.CanruleId;
            object canRule = null;
            if (CanruleId != null)
            {
                canRule = await PrepareCanrulesAsync(CanruleId.ToString());
            }

            object desired = new
            {
                // driver_list = await PrepareDriverListAsync(vehicle), // there is already a method to call the driverlist separately
                check_list = await PrepareChecklistAsync(vehicle),
                multilang_check_list = await PrepareMultiLanguageChecklistAsync(deviceId),
                checklist_time = await PrepareChecklistSettingAsync(vehicle),
                survey_timeout_setting = await PrepareSurveyTimeoutAsync(vehicle),
                comment = await PrepareShowCommentAsync(vehicle),
                preop_randomization = await PrepareRandomizeAsync(vehicle),
                multilang_enabled = await PrepareIsMultilangEnabledAsync(vehicle),
                tzone = await PrepareTimezoneAsync(vehicle),
                idle_setting = idleSetting,
                impact_lockout = impactLockout,
                report_interval_sec = reportIntervalSec,
                can = canRule,
                convor = await PrepareVORSettingsAsync(vehicle),
                full_lockout_setting = await PrepareFullLockoutSettingsAsync(vehicle),
                wifi_settings = await PrepareWifiSettingsAsync(vehicle),
                (await PrepareSyncSafeTySettings(vehicle))?.digital_input,
                (await PrepareSyncSafeTySettings(vehicle))?.seen_safety,
                amber_impact_alert = await PrepareAmberImpactAlertAsync(vehicle),
                font = CreateFontArray(departmentChecklist),
            };

            module.DeviceTwin = JsonConvert.SerializeObject(desired);
            await _dataFacade.ModuleDataProvider.SaveAsync(module);
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 0); // syncType = 0
            await SyncDriverToVehicle(deviceId);
        }

        public async Task SyncTimezone(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var desired = new
            {
                tzone = await PrepareTimezoneAsync(data.Vehicle)
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 4);
        }

        public async Task SyncVORSettingsAsync(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var desired = new
            {
                convor = await PrepareVORSettingsAsync(data.Vehicle)
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 5);
        }


        public async Task SendWebVORUpdateToVehicleAsync(string deviceId, bool? status)
        {
            var desired = new
            {
                convor = status
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 5);
        }

        public async Task SyncFullLockOutSettingsAsync(string deviceId)
        {
            var data = await GetModuleAndVehicleAsync(deviceId);
            if (data.Module == null || data.Vehicle == null)
            {
                return;
            }

            var desired = new
            {
                full_lockout_setting = await PrepareFullLockoutSettingsAsync(data.Vehicle)
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 6);
        }

        public async Task UpdateFirmware(string deviceId, String FirmwareVersion)
        {
            var firmwareDTO = (await _dataFacade.FirmwareDataProvider.GetCollectionAsync(null, "Version == @0", new object[] { FirmwareVersion })).FirstOrDefault();

            var desired = new
            {
                firmware = new
                {
                    vFw = firmwareDTO.Version,
                    vFwUrl = firmwareDTO.Url
                }
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 7);
        }

        public async Task UpdateCANRuleAsync(string deviceId, String CanruleId)
        {

            var desired = new
            {
                can = PrepareCanrulesAsync(CanruleId)
            };
            await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 8);
        }

        public async Task SyncLogoAsync(string deviceId, string logoUrl)
        {
            try
            {
                var data = await GetModuleAndVehicleAsync(deviceId);
                if (data.Module == null || data.Vehicle == null)
                {
                    return;
                }

                var desired = new
                {
                    logo_url = logoUrl
                };

                await _deviceMessageHandler.UpdateDesiredProperties(deviceId, desired, 0); // syncType = 0 (All Setting)

                _logger.LogInformation($"Logo synced to deviceId {deviceId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to sync logo to device {deviceId}: {ex.Message}");
                // Fire and forget - don't rethrow the exception
            }
        }

        private async Task<object> PrepareOnDemandSettingAsync(VehicleDataObject vehicle)
        {
            var onDemandSettings = await vehicle.LoadOnDemandSettingsAsync();

            if (onDemandSettings == null)
            {
                throw new GOServerException($"OnDemandSettings not found for vehicle {vehicle.Id}");
            }

            var onDemandPerson = await vehicle.LoadPersonAsync();

            if (onDemandPerson == null)
            {
                throw new GOServerException($"OnDemandPerson not found for vehicle {vehicle.Id}");
            }

            // get the card of the person from person.Driver
            var card = await (await onDemandPerson.LoadDriverAsync()).LoadCardAsync();
            if (card == null)
            {
                throw new GOServerException($"Card not found for person {onDemandPerson.Id}");
            }

            return new
            {
                id = card.Weigand,
                cmd = (int)onDemandSettings.OnDemandCommand,
                sessionTimeMin = onDemandSettings.SessionTime
            };
        }
        private async Task<object> PrepareDriverListAsync(VehicleDataObject vehicle)
        {
            var driverList = new List<dynamic>();
            var masterList = new List<dynamic>();
            var techList = new List<dynamic>();
            var ondemandList = new List<dynamic>();

            // Cache permissions to avoid repeated queries
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            var permissionMaster = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master }, skipSecurity: true)).SingleOrDefault();
            var permissionOnDemand = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();

            // Get vehicle site, model, and department once
            var site = await vehicle.LoadSiteAsync(skipSecurity: true);
            var model = await vehicle.LoadModelAsync(skipSecurity: true);
            var department = await vehicle.LoadDepartmentAsync(skipSecurity: true);

            // Get all vehicle access data to find which persons have access
            var vehicleAccess = await _dataFacade.PersonToPerVehicleNormalAccessViewDataProvider.GetCollectionAsync(
                null,
                "VehicleId == @0 && HasAccess == @1",
                new object[] { vehicle.Id, true },
                skipSecurity: true);

            if (!vehicleAccess.Any())
            {
                _logger?.LogInformation($"[PERF] No vehicle access found for vehicle {vehicle.Id}");
                return new
                {
                    reg = DataUtils.CompressAndSplit(driverList),
                    master = DataUtils.CompressAndSplit(masterList),
                    tech = DataUtils.CompressAndSplit(techList),
                    smast = DataUtils.CompressAndSplit(ondemandList),
                };
            }

            // Get all unique person IDs that have access to this vehicle
            var personIds = vehicleAccess.Select(va => va.PersonId).Distinct().ToList();

            // Create a lookup for PersonId to multiple PermissionIds (handles duplicate PersonIds)
            var personPermissionLookup = vehicleAccess.ToLookup(va => va.PersonId, va => va.PermissionId);

            // Bulk load all persons with their related data using includes to minimize queries
            // Use a more compatible approach for the IN clause
            var persons = new List<PersonDataObject>();

            if (personIds.Count > 0)
            {
                // For small numbers, use OR conditions; for large numbers, batch the queries
                if (personIds.Count <= 100)
                {
                    // Build OR conditions for better compatibility
                    var orConditions = string.Join(" || ", personIds.Select((_, i) => $"Id == @{i}"));
                    persons.AddRange(await _dataFacade.PersonDataProvider.GetCollectionAsync(
                        null,
                        orConditions,
                        personIds.Cast<object>().ToArray(),
                        includes: new List<string> { "Driver", "Driver.Card", "PersonChecklistLanguageSettings" },
                        skipSecurity: true));
                }
                else
                {
                    // For large datasets, batch the queries to avoid query size limits
                    const int batchSize = 100;
                    for (int i = 0; i < personIds.Count; i += batchSize)
                    {
                        var batch = personIds.Skip(i).Take(batchSize).ToList();
                        var orConditions = string.Join(" || ", batch.Select((_, j) => $"Id == @{j}"));
                        var batchResults = await _dataFacade.PersonDataProvider.GetCollectionAsync(
                            null,
                            orConditions,
                            batch.Cast<object>().ToArray(),
                            includes: new List<string> { "Driver", "Driver.Card", "PersonChecklistLanguageSettings" },
                            skipSecurity: true);
                        persons.AddRange(batchResults);
                    }
                }
            }

            // Create lookup dictionaries for fast access
            var notAllowedDriversSet = new HashSet<string>();

            // Process each person and categorize them
            foreach (var person in persons)
            {
                try
                {
                    // Get all permissions for this person
                    var personPermissions = personPermissionLookup[person.Id];
                    if (!personPermissions.Any())
                        continue;

                    var driver = person.Driver;
                    if (driver == null) continue;

                    var card = driver.Card;
                    if (card == null) continue;

                    // License validation
                    bool licenseAllowed = true;
                    if (person.LicenseActive)
                    {
                        if (driver.LicenseMode == ModeEnum.General)
                        {
                            var generalLicense = await driver.LoadGeneralLicenceAsync(skipSecurity: true);
                            if (generalLicense != null && generalLicense.ExpiryDate < DateTime.Now)
                            {
                                licenseAllowed = false;
                            }
                        }
                        else if (driver.LicenseMode == ModeEnum.ByModel)
                        {
                            var modelLicenses = await driver.LoadLicensesByModelAsync(skipSecurity: true);
                            licenseAllowed = modelLicenses.Any(license =>
                                license.ModelId == model.Id && license.ExpiryDate > DateTime.Now);
                        }
                    }

                    bool isDriverAllowed = driver.Active && card.Active && licenseAllowed;

                    if (!isDriverAllowed)
                    {
                        notAllowedDriversSet.Add(card.Weigand);
                        continue;
                    }

                    var language = GetLanguageCode(person.PersonChecklistLanguageSettings?.Language);

                    // Process each permission this person has for this vehicle
                    foreach (var permissionId in personPermissions)
                    {
                        // Normal drivers
                        if (permissionId == permissionDriver.Id)
                        {
                            var driverObject = new
                            {
                                name = person.FullName,
                                id = card.Weigand,
                                language = language
                            };

                            if (!driverList.Any(x => x.id == driverObject.id))
                            {
                                driverList.Add(driverObject);
                            }
                        }

                        // Master/Supervisor drivers
                        if (permissionId == permissionMaster.Id && person.Supervisor == true)
                        {
                            var masterObject = new
                            {
                                name = person.FullName,
                                id = card.Weigand,
                                option = person.MasterMenuOptions,
                                language = language
                            };

                            if (!masterList.Any(x => x.id == masterObject.id) &&
                                !notAllowedDriversSet.Contains(masterObject.id))
                            {
                                masterList.Add(masterObject);
                            }
                        }

                        // OnDemand users
                        if (permissionId == permissionOnDemand.Id && person.OnDemand == true)
                        {
                            var onDemandObject = new
                            {
                                name = person.FullName,
                                super_id = card.Weigand,
                                language = language
                            };

                            if (!ondemandList.Any(x => x.super_id == onDemandObject.super_id) &&
                                !notAllowedDriversSet.Contains(onDemandObject.super_id))
                            {
                                ondemandList.Add(onDemandObject);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Error processing person {person.Id} for vehicle {vehicle.Id}: {ex.Message}");
                    continue;
                }
            }

            // Handle dealer drivers efficiently
            try
            {
                var customer = await vehicle.LoadCustomerAsync(skipSecurity: true);
                var dealer = await customer.LoadDealerAsync(skipSecurity: true);

                // Bulk load dealer drivers with related entities using includes
                var dealerDriverGOUsers = await _dataFacade.GOUserDataProvider.GetCollectionAsync(
                    null,
                    "DealerId == @0",
                    new object[] { dealer.Id },
                    includes: new List<string> { "DealerDriver", "DealerDriver.Card" },
                    skipSecurity: true);

                foreach (var goUser in dealerDriverGOUsers)
                {
                    try
                    {
                        var dealerDriver = goUser.DealerDriver;
                        if (dealerDriver?.Card == null || !dealerDriver.Card.Active)
                            continue;

                        var dealerObject = new
                        {
                            name = goUser.FullName,
                            id = dealerDriver.Card.Weigand
                        };

                        if (!techList.Any(x => x.id == dealerObject.id) &&
                            !notAllowedDriversSet.Contains(dealerObject.id))
                        {
                            techList.Add(dealerObject);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning($"Error processing dealer driver for GOUser {goUser.Id}: {ex.Message}");
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"Error processing dealer drivers for vehicle {vehicle.Id}: {ex.Message}");
            }

            // Add default tech access if enabled
            try
            {
                var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync(skipSecurity: true);
                var defaultTechAccess = otherSettings?.DefaultTechnicianAccess ?? false;

                if (defaultTechAccess)
                {
                    var defaultTechObject = new
                    {
                        name = "Default Tech",
                        id = "0x2002f5b"
                    };
                    techList.Add(defaultTechObject);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"Error processing default tech access for vehicle {vehicle.Id}: {ex.Message}");
            }

            _logger?.LogInformation($"[PERF] PrepareDriverListAsync completed for vehicle {vehicle.Id}: " +
                                  $"{driverList.Count} regular, {masterList.Count} master, " +
                                  $"{techList.Count} tech, {ondemandList.Count} ondemand drivers");

            return new
            {
                reg = DataUtils.CompressAndSplit(driverList),
                master = DataUtils.CompressAndSplit(masterList),
                tech = DataUtils.CompressAndSplit(techList),
                smast = DataUtils.CompressAndSplit(ondemandList),
            };
        }

        private async Task<object> PrepareChecklistAsync(VehicleDataObject vehicle)
        {
            var department = await vehicle.LoadDepartmentAsync(skipSecurity: true);
            var model = await vehicle.LoadModelAsync(skipSecurity: true);
            //sort by order
            var vehicleToPreOpChecklists = (await _dataFacade.VehicleToPreOpChecklistViewDataProvider.GetCollectionAsync(null, "VehicleId == @0 && Active == @1", new object[] { vehicle.Id, true }, skipSecurity: true)).ToList();
            if (vehicleToPreOpChecklists == null || !vehicleToPreOpChecklists.Any())
            {
                return null;
            }

            var questions = new List<PreOperationalChecklistDataObject>();

            foreach (var vehicleToPreOpCheckList in vehicleToPreOpChecklists)
            {
                var question = await vehicleToPreOpCheckList.LoadPreOperationalChecklistAsync(skipSecurity: true);
                questions.Add(question);
            }

            var checkList = new List<object>();
            var preopChecklistItems = questions.OrderBy(x => x.Order).ToList();
            foreach (var question in preopChecklistItems)
            {
                //<do_not_randomise> specifies whether the question can be reordered if checklist randomisation is enabled (optional, default is 0):
                //0 = randomise
                //1 = do not randomise
                var checkListItem = new
                {
                    id = question.Id,
                    type = question.Critical ? (question.ExpectedAnswer ? 1 : 2) : 0,
                    randomize = question.ExcludeRandom ? 1 : 0,
                    question = question.Question
                };
                checkList.Add(checkListItem);
            }

            return DataUtils.CompressAndSplit(checkList);
        }


        public async Task<string> PrepareMultiLanguageChecklistAsync(string deviceId)
        {
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { deviceId }, skipSecurity: true)).SingleOrDefault();
            if (module != null)
            {
                var vehicle = await module.LoadVehicleAsync(skipSecurity: true);
                if (vehicle == null)
                {
                    throw new GOServerException($"unknow vehicle id {deviceId}");
                }

                var departmentChecklist = await vehicle.LoadDepartmentChecklistAsync(skipSecurity: true);

                // Get the actual checklist questions for this vehicle
                var vehicleToPreOpChecklists = (await _dataFacade.VehicleToPreOpChecklistViewDataProvider.GetCollectionAsync(null, "VehicleId == @0 && Active == @1", new object[] { vehicle.Id, true }, skipSecurity: true)).ToList();
                if (vehicleToPreOpChecklists == null || !vehicleToPreOpChecklists.Any())
                {
                    return null;
                }

                var questions = new List<PreOperationalChecklistDataObject>();
                foreach (var vehicleToPreOpCheckList in vehicleToPreOpChecklists)
                {
                    var question = await vehicleToPreOpCheckList.LoadPreOperationalChecklistAsync(skipSecurity: true);
                    questions.Add(question);
                }

                // Create multilanguage checklist with actual questions
                var checklistItems = new List<object>();
                var preopChecklistItems = questions.OrderBy(x => x.Order).ToList();

                foreach (var question in preopChecklistItems)
                {
                    // Create question object with only enabled languages
                    var questionObj = new
                    {
                        id = question.Id.ToString(),
                        type = question.Critical ? (question.ExpectedAnswer ? 1 : 2) : 0,
                        randomize = question.ExcludeRandom,
                        question = CreateQuestionObject(question, departmentChecklist)
                    };
                    checklistItems.Add(questionObj);
                }

                var multiLanguageChecklist = new
                {
                    checklist = checklistItems,
                    font = CreateFontArray(departmentChecklist)
                };

                var jsonString = JsonConvert.SerializeObject(multiLanguageChecklist);

                // Generate unique blob name using deviceId
                string blobName = $"checklists/{deviceId}/multilang.json";
                string containerName = _configuration["AzureStorage:ContainerName"];

                // Use the factory to create the blob client
                var blobClient = _storageClientFactory.CreateBlobClient(containerName, blobName);

                using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonString)))
                {
                    await blobClient.UploadAsync(stream, overwrite: true);
                }

                _logger.LogInformation($"Generated multilanguage checklist for device {deviceId} with {checklistItems.Count} questions");
                return blobClient.Uri.ToString();
            }

            return null;
        }

        private async Task<object> PrepareChecklistSettingAsync(VehicleDataObject vehicle)
        {
            var checklistSettings = await vehicle.LoadChecklistSettingsAsync(skipSecurity: true);

            if (checklistSettings == null)
            {
                return null;
            }

            var checkListTimes = new List<object>();
            if (checklistSettings.Type == Type1Enum.DriverBase) // driver base
            {
                checkListTimes = new List<object> {
                    new { time_slot = 9999 },
                    new { time_slot = 9999 },
                    new { time_slot = 9999 },
                    new { time_slot = 9999 }
                };
            }
            else if (checklistSettings.Type == Type1Enum.Timebase) // time base
            {
                checkListTimes = new List<object>
                {
                    new { time_slot = checklistSettings.TimeslotOne.HasValue ? int.Parse(checklistSettings.TimeslotOne?.ToString(@"hhmm")) : -1 },
                    new { time_slot = checklistSettings.TimeslotTwo.HasValue ? int.Parse(checklistSettings.TimeslotTwo?.ToString(@"hhmm")) : -1 },
                    new { time_slot = checklistSettings.TimeslotThree.HasValue ? int.Parse(checklistSettings.TimeslotThree?.ToString(@"hhmm")) : -1 },
                    new { time_slot = checklistSettings.TimeslotFour.HasValue ? int.Parse(checklistSettings.TimeslotFour?.ToString(@"hhmm")) : -1 }
                };
            }

            return checkListTimes;
        }

        private short? HourOfDayToShort(HourOfDayEnum? hourOfDayEnum)
        {
            if (hourOfDayEnum == null)
            {
                return null;
            }

            return (short?)hourOfDayEnum;
        }

        private short? MinuteOfHourToShort(MinuteOfHourEnum? minuteOfHourEnum)
        {
            if (minuteOfHourEnum == null)
            {
                return null;
            }

            return (short?)minuteOfHourEnum;
        }

        private async Task<object> PrepareSurveyTimeoutAsync(VehicleDataObject vehicle)
        {
            var checklistSettings = await vehicle.LoadChecklistSettingsAsync(skipSecurity: true);
            return checklistSettings?.QuestionTimeout;
        }

        private async Task<object> PrepareShowCommentAsync(VehicleDataObject vehicle)
        {
            var checklistSettings = await vehicle.LoadChecklistSettingsAsync(skipSecurity: true);
            return checklistSettings?.ShowComment;
        }

        private async Task<object> PrepareRandomizeAsync(VehicleDataObject vehicle)
        {
            var checklistSettings = await vehicle.LoadChecklistSettingsAsync(skipSecurity: true);
            return checklistSettings?.Randomisation;
        }

        private async Task<object> PrepareIsMultilangEnabledAsync(VehicleDataObject vehicle)
        {
            var multilangEnabled = await vehicle.LoadDepartmentChecklistAsync(skipSecurity: true);
            return (multilangEnabled?.IsSpanishEnabled == true ||
                    multilangEnabled?.IsThaiEnabled == true ||
                    multilangEnabled?.IsFrenchEnabled == true ||
                    multilangEnabled?.IsFilipinoEnabled == true ||
                    multilangEnabled?.IsVietnameseEnabled == true ||
                    multilangEnabled?.IsTraditionalChineseEnabled == true);
        }

        private object CreateQuestionObject(PreOperationalChecklistDataObject question, DepartmentChecklistDataObject departmentChecklist)
        {
            // Always include English as the base language
            var questionObj = new Dictionary<string, object>
            {
                ["en"] = question.Question
            };

            // Conditionally add other languages based on department checklist flags
            if (departmentChecklist?.IsSpanishEnabled == true)
            {
                questionObj["es"] = question.SpanishQuestion ?? question.Question;
            }

            if (departmentChecklist?.IsThaiEnabled == true)
            {
                questionObj["th"] = question.ThaiQuestion ?? question.Question;
            }

            if (departmentChecklist?.IsFrenchEnabled == true)
            {
                questionObj["fr"] = question.FrenchQuestion ?? question.Question;
            }

            if (departmentChecklist?.IsFilipinoEnabled == true)
            {
                questionObj["tl"] = question.FilipinoQuestion ?? question.Question;
            }

            if (departmentChecklist?.IsVietnameseEnabled == true)
            {
                questionObj["vi"] = question.VietnameseQuestion ?? question.Question;
            }

            if (departmentChecklist?.IsTraditionalChineseEnabled == true)
            {
                questionObj["zh"] = question.TraditionalChineseQuestion ?? question.Question;
            }

            return questionObj;
        }

        private List<string> CreateFontArray(DepartmentChecklistDataObject departmentChecklist = null)
        {
            var fonts = new List<string>();

            // Only add fonts for languages that are enabled in the department checklist
            if (departmentChecklist?.IsThaiEnabled == true)
            {
                fonts.Add("https://fleetxqfiles.blob.core.windows.net/devicetwin/fonts/th_itim_regular.otf");
            }

            if (departmentChecklist?.IsVietnameseEnabled == true)
            {
                fonts.Add("https://fleetxqfiles.blob.core.windows.net/devicetwin/fonts/BeVietnamPro-Regular.ttf");
            }

            if (departmentChecklist?.IsTraditionalChineseEnabled == true)
            {
                fonts.Add("https://fleetxqfiles.blob.core.windows.net/devicetwin/fonts/NotoSansTC-Regular.ttf");
            }

            return fonts;
        }

        private async Task<object> PrepareTimezoneAsync(VehicleDataObject vehicle)
        {
            var tzone = await vehicle.LoadSiteAsync(skipSecurity: true).Result.LoadTimezoneAsync(skipSecurity: true);
            return tzone.UTCOffset * 60;
        }

        private async Task<object> PrepareVORSettingsAsync(VehicleDataObject vehicle)
        {
            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync(skipSecurity: true);
            return otherSettings?.VORStatus;
        }

        private async Task<object> PrepareFullLockoutSettingsAsync(VehicleDataObject vehicle)
        {
            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync(skipSecurity: true);
            return new
            {
                enable = otherSettings?.FullLockout == true,
                timeout = otherSettings?.FullLockoutTimeout ?? 0
            };
        }

        private async Task<object> PrepareWifiSettingsAsync(VehicleDataObject vehicle)
        {
            var networkSettings = await vehicle.LoadNetworkSettingsItemsAsync(skipSecurity: true);
            if (networkSettings == null)
            {
                return null;
            }

            return networkSettings.Select(x => new
            {
                ssid = x.SSID,
                password = x.WifiPassword,
                country = "AU"
            }).ToList();
        }

        private async Task<SeenSafetySettings> PrepareSyncSafeTySettings(VehicleDataObject vehicle)
        {
            SeenSafetySettings safetySettings = new SeenSafetySettings();
            var vehicleOtherSettings = await vehicle.LoadVehicleOtherSettingsAsync(skipSecurity: true);
            if (vehicleOtherSettings == null)
            {
                return null;
            }

            var pedestrianSafetySettings = vehicleOtherSettings.PedestrianSafety;
            if (pedestrianSafetySettings)
            {
                safetySettings.digital_input = new
                {
                    cfg1 = 0,
                    cfg2 = 0,
                    cfg3 = 3,
                    cfg4 = 0
                };
                safetySettings.seen_safety = new
                {
                    debounceMs = 1500,
                    holdMs = 10000
                };
            }
            return safetySettings;
        }

        private async Task<object> PrepareCanrulesAsync(String CanruleId)
        {
            // parse the canrule id to guid
            var canRuleList = (await _dataFacade.CanruleDetailsDataProvider.GetCollectionAsync(null, "CanruleId == @0", new object[] { Guid.Parse(CanruleId) })).OrderBy(x => x.Canrule).ToList();

            object cfg = null;
            object pgn = null;
            object att = null;
            object spn = null;
            object lin = null;
            object lin2 = null;
            object byd = null;

            // Helper method to extract index from parts[0] or use fallback
            int GetIndexFromParts(string part0, int fallbackIndex)
            {
                // Try to extract numeric suffix from parts[0] (e.g., "CANPGN0" -> 0, "CANATT1" -> 1)
                var match = System.Text.RegularExpressions.Regex.Match(part0, @"(\d+)$");
                if (match.Success && int.TryParse(match.Value, out int index))
                {
                    return index;
                }
                return fallbackIndex;
            }

            if (canRuleList.Count() == 0)
            {
                throw new GOServerException($"unknow canrule id {CanruleId}");
            }
            foreach (var canRule in canRuleList)
            {
                var canRuleDetail = canRule.Canrules;
                string[] parts = canRuleDetail.ToString().Split(',');
                if (parts[0].StartsWith("CANCFG"))
                {
                    cfg = new
                    {
                        protocol = int.Parse(parts[0].Replace("CANCFG=", "")),
                        baud = int.Parse(parts[1]),
                        extended = parts[2] == "1",
                        enabled = parts[3] == "1"
                    };
                }
                if (parts[0].StartsWith("CANPGN"))
                {
                    if (pgn == null)
                    {
                        pgn = new List<object>();
                    }
                    var pgnObject = new
                    {
                        index = GetIndexFromParts(parts[0], (pgn as List<object>).Count),
                        extended = parts[1] == "1",
                        pgn = DataUtils.ConvertHexToInt(parts[2]),
                        prio = DataUtils.ConvertHexToInt(parts[3]),
                        sourceAddress = DataUtils.ConvertHexToInt(parts[4]),
                        pollingX10ms = DataUtils.ConvertHexToInt(parts[5])
                    };
                    (pgn as List<object>).Add(pgnObject);
                }
                if (parts[0].StartsWith("CANATT"))
                {
                    var attObject = new
                    {
                        index = GetIndexFromParts(parts[0], att == null ? 0 : (att as List<object>).Count),
                        name = parts[1],
                        type = DataUtils.ConvertHexToInt(parts[2]),
                        spnIndex = DataUtils.ConvertHexToInt(parts[3]),
                        spnMask = DataUtils.ConvertHexToInt(parts[4]),
                        spnState = DataUtils.ConvertHexToInt(parts[5])
                    };
                    if (att == null)
                    {
                        att = new List<object>();
                    }
                    (att as List<object>).Add(attObject);
                }
                if (parts[0].StartsWith("CANSPN"))
                {
                    var spnObject = new
                    {
                        index = GetIndexFromParts(parts[0], spn == null ? 0 : (spn as List<object>).Count),
                        pgnIndex = DataUtils.ConvertHexToInt(parts[1]),
                        siblingIndex = DataUtils.ConvertHexToInt(parts[2]),
                        spn = DataUtils.ConvertHexToInt(parts[3]),
                        width = DataUtils.ConvertHexToInt(parts[4]),
                        offset = DataUtils.ConvertHexToInt(parts[5]),
                        opcode = parts[6],
                        operand = DataUtils.ConvertHexToInt(parts[7])
                    };
                    if (spn == null)
                    {
                        spn = new List<object>();
                    }
                    (spn as List<object>).Add(spnObject);
                }
                if (parts[0].StartsWith("CANLIN"))
                {
                    if (parts[0].StartsWith("CANLIN2"))
                    {
                        try
                        {
                            // For 64-bit hex values like address (6 -> 0000000000000006)
                            string paddedAddr = parts[3].PadLeft(16, '0');
                            string paddedSpnRsp = parts[4].PadLeft(16, '0');
                            string paddedSpnRspMask = parts[5].PadLeft(16, '0');

                            var linObject = new
                            {
                                index = GetIndexFromParts(parts[0], lin2 == null ? 0 : (lin2 as List<object>).Count),
                                rspId = Convert.ToInt32(parts[1], 16),  // Convert hex to decimal (2f8 -> 760)
                                reqId = Convert.ToInt32(parts[2], 16),
                                // Split address into high/low components
                                addrHi = Convert.ToInt64(paddedAddr.Substring(0, 8), 16),
                                addrLo = Convert.ToInt64(paddedAddr.Substring(8, 8), 16),
                                spnRspHi = Convert.ToInt64(paddedSpnRsp.Substring(0, 8), 16),
                                spnRspLo = Convert.ToInt64(paddedSpnRsp.Substring(8, 8), 16),
                                spnRspMaskHi = Convert.ToInt64(paddedSpnRspMask.Substring(0, 8), 16),
                                spnRspMaskLo = Convert.ToInt64(paddedSpnRspMask.Substring(8, 8), 16),
                                width = Convert.ToInt32(parts[6], 16),
                                offset = Convert.ToInt32(parts[7], 16),
                                opcode = parts[8],
                                operand = Convert.ToInt32(parts[9], 16)
                            };

                            if (lin2 == null)
                            {
                                lin2 = new List<object>();
                            }
                            (lin2 as List<object>).Add(linObject);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, $"Error parsing LIN2: {ex.Message}");
                            throw new GOServerException($"Error parsing LIN2: {ex.Message}");
                        }
                    }
                    else
                    {

                        var linObject = new
                        {
                            index = GetIndexFromParts(parts[0], lin == null ? 0 : (lin as List<object>).Count),
                            identifier = DataUtils.ConvertHexToInt(parts[1]),
                            address = DataUtils.ConvertHexToInt(parts[2]),
                            width = DataUtils.ConvertHexToInt(parts[3]),
                            offset = DataUtils.ConvertHexToInt(parts[4]),
                            opcode = parts[5],
                            operand = DataUtils.ConvertHexToInt(parts[6])
                        };
                        if (lin == null)
                        {
                            lin = new List<object>();
                        }
                    (lin as List<object>).Add(linObject);
                    }
                }

                if (parts[0].StartsWith("CANBYD"))
                {
                    var bydObject = new
                    {
                        index = GetIndexFromParts(parts[0], byd == null ? 0 : (byd as List<object>).Count),
                        identifier = DataUtils.ConvertHexToInt(parts[1]),
                        address = DataUtils.ConvertHexToInt(parts[2]),
                        width = DataUtils.ConvertHexToInt(parts[3]),
                        offset = DataUtils.ConvertHexToInt(parts[4]),
                        opcode = parts[5],
                        operand = DataUtils.ConvertHexToInt(parts[6])
                    };
                    if (byd == null)
                    {
                        byd = new List<object>();
                    }
                    (byd as List<object>).Add(bydObject);
                }
            }

            return new
            {
                cfg,
                pgn,
                spn,
                lin,
                lin2,
                byd,
                att
            };
        }

        private async Task<object> PrepareAmberImpactAlertAsync(VehicleDataObject vehicle)
        {
            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync();
            return otherSettings?.AmberAlertEnabled ?? false;
        }

        // Helper method to get module and vehicle data
        private async Task<(ModuleDataObject Module, VehicleDataObject Vehicle)> GetModuleAndVehicleAsync(string deviceId)
        {
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { deviceId }, skipSecurity: true)).SingleOrDefault();
            if (module == null)
            {
                return (null, null);
            }

            var vehicle = await module.LoadVehicleAsync(skipSecurity: true);

            // Exclude soft-deleted vehicles
            if (vehicle != null && vehicle.DeletedAtUtc != null)
            {
                return (module, null);
            }

            return (module, vehicle);
        }
    }

    //create a new object for SeenSafetySettings
    public class SeenSafetySettings
    {
        public object digital_input { get; set; }
        public object seen_safety { get; set; }
    }
}
