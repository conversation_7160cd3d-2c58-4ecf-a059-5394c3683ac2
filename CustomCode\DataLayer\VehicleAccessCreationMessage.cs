using System;

namespace FleetXQ.Data.DataObjects
{
    public class VehicleAccessCreationMessage
    {
        public Guid VehicleId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ModelId { get; set; }
        public Guid DepartmentId { get; set; }
        public Guid SiteId { get; set; }
        public bool IsNewVehicle { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string IoTDevice { get; set; }
        public int RetryCount { get; set; } = 0;
        public int MaxRetries { get; set; } = 3;

        // Properties for department change processing
        public bool IsDepartmentChange { get; set; } = false;
        public Guid? OldDepartmentId { get; set; }
        public Guid? OldSiteId { get; set; }
    }
}